import { jsx, jsxs, Fragment } from "react/jsx-runtime";
import { useAnimation, motion, useInView, AnimatePresence, useCycle } from "framer-motion";
import React, { useRef, useMemo, useCallback, useEffect, useState } from "react";
import { SmileyIcon, TwitterLogoIcon, DribbbleLogoIcon, LinkedinLogoIcon, ArrowLeftIcon, ClockIcon, CalendarBlankIcon, TagIcon, ArrowUpRightIcon, ChatsIcon, PhoneCallIcon, MapPinIcon, RocketIcon, CaretDownIcon, ArrowRightIcon, UsersThreeIcon, GearIcon, HouseIcon } from "@phosphor-icons/react";
import { Head, Link, usePage, createInertiaApp } from "@inertiajs/react";
import { route } from "ziggy-js";
import { renderToString } from "react-dom/server";
function ScrollReveal({
  children,
  className = "",
  delay = 0,
  duration = 0.3,
  style = {},
  as = "div",
  alt,
  src,
  threshold = 0.15,
  rootMargin = "0px",
  triggerOnce = false,
  customEasing = [0.25, 0.46, 0.45, 0.94],
  // cubic-bezier for smooth easing
  translateY = 40,
  disabled = false
}) {
  const elementRef = useRef(null);
  const observerRef = useRef(null);
  const lastScrollY = useRef(
    typeof window !== "undefined" ? window.scrollY : 0
  );
  const animationFrameRef = useRef(null);
  const hasAnimated = useRef(false);
  const isScrollingDown = useRef(true);
  const controls = useAnimation();
  const prefersReducedMotion = useMemo(() => {
    if (typeof window === "undefined") return false;
    return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
  }, []);
  const debouncedScrollHandler = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    animationFrameRef.current = requestAnimationFrame(() => {
      try {
        const currentScrollY = window.scrollY;
        isScrollingDown.current = currentScrollY > lastScrollY.current;
        lastScrollY.current = currentScrollY;
      } catch (error) {
        console.warn("ScrollReveal: Error in scroll handler:", error);
      }
    });
  }, []);
  const handleIntersection = useCallback(
    (entries) => {
      try {
        const [entry] = entries;
        const isIntersecting = entry.isIntersecting;
        const shouldAnimateIn = isIntersecting && isScrollingDown.current && (!triggerOnce || !hasAnimated.current) && !disabled;
        if (shouldAnimateIn) {
          hasAnimated.current = true;
          controls.start({
            opacity: 1,
            y: 0,
            transition: {
              duration: prefersReducedMotion ? 0 : duration,
              delay: prefersReducedMotion ? 0 : delay,
              ease: customEasing
            }
          });
        } else if (!isIntersecting && !triggerOnce && !hasAnimated.current) {
          controls.start({
            opacity: 0,
            y: translateY,
            transition: {
              duration: prefersReducedMotion ? 0 : duration * 0.5,
              ease: customEasing
            }
          });
        }
      } catch (error) {
        console.warn(
          "ScrollReveal: Error in intersection handler:",
          error
        );
      }
    },
    [
      controls,
      delay,
      duration,
      triggerOnce,
      customEasing,
      translateY,
      disabled,
      prefersReducedMotion
    ]
  );
  useEffect(() => {
    if (!elementRef.current || typeof window === "undefined") return;
    try {
      observerRef.current = new IntersectionObserver(handleIntersection, {
        threshold,
        rootMargin,
        // Use passive observation for better performance
        passive: true
      });
      observerRef.current.observe(elementRef.current);
      window.addEventListener("scroll", debouncedScrollHandler, {
        passive: true
      });
      return () => {
        if (observerRef.current) {
          observerRef.current.disconnect();
        }
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
        window.removeEventListener("scroll", debouncedScrollHandler);
      };
    } catch (error) {
      console.warn("ScrollReveal: Error initializing observer:", error);
    }
  }, [handleIntersection, threshold, rootMargin, debouncedScrollHandler]);
  const MotionComponent = useMemo(() => {
    try {
      return motion[as] || motion.div;
    } catch (error) {
      console.warn(
        "ScrollReveal: Invalid motion component, falling back to div:",
        error
      );
      return motion.div;
    }
  }, [as]);
  const extraProps = useMemo(() => {
    const props = {};
    if (as === "img") {
      if (alt) props.alt = alt;
      if (src) props.src = src;
    }
    return props;
  }, [as, alt, src]);
  const optimizedStyle = useMemo(
    () => ({
      ...style,
      // Enable hardware acceleration
      willChange: "transform, opacity",
      // Ensure proper layering
      transform: "translateZ(0)",
      ...style
      // Allow style overrides
    }),
    [style]
  );
  if (disabled || prefersReducedMotion) {
    const StaticComponent = as === "div" ? "div" : as;
    return /* @__PURE__ */ jsx(
      StaticComponent,
      {
        ref: elementRef,
        className,
        style: optimizedStyle,
        ...extraProps,
        children
      }
    );
  }
  return /* @__PURE__ */ jsx(
    MotionComponent,
    {
      ref: elementRef,
      initial: {
        opacity: 0,
        y: translateY,
        // Ensure transforms are GPU-accelerated
        transform: "translateZ(0)"
      },
      animate: controls,
      className,
      style: optimizedStyle,
      ...extraProps,
      children
    }
  );
}
const VendorSection = () => {
  return /* @__PURE__ */ jsx("div", { className: "bg-transparent py-10", children: /* @__PURE__ */ jsxs("div", { className: "mx-auto flex max-w-[1200px] flex-col items-center rounded-[32px] bg-white px-8 pt-12 pb-8 shadow-sm", children: [
    /* @__PURE__ */ jsxs(ScrollReveal, { className: "tablet:flex-row mx-4 mb-8 flex w-full flex-col items-center justify-between", children: [
      /* @__PURE__ */ jsx("h1", { className: "tablet:w-[524px] tablet:text-left tablet:text-4xl tablet:font-bold mx-auto mb-4 justify-start text-center text-2xl leading-tight", children: "Not sure how to choose a vendor?" }),
      /* @__PURE__ */ jsx("p", { className: "tablet:w-[420px] tablet:text-left tablet:text-lg mx-auto mb-8 max-w-[600px] justify-end text-center text-base font-normal text-[#222]", children: "At Fulcrum we always find a way, also, we are courageously honest and we most certainly give a damn." })
    ] }),
    /* @__PURE__ */ jsx(
      ScrollReveal,
      {
        as: "img",
        src: "/images/misc/vendor-workstation.jpg",
        alt: "Workstation",
        className: "block h-[360px] w-full max-w-[1136px] rounded-[28px] object-cover"
      }
    )
  ] }) });
};
const DirectorSection = () => /* @__PURE__ */ jsx("div", { className: "flex w-full flex-col items-center bg-transparent py-8", children: /* @__PURE__ */ jsx("div", { className: "tablet:px-16 mx-auto flex w-full max-w-[1200px] flex-col items-center rounded-[32px] bg-white px-4 py-16 shadow-sm", children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center", children: [
  /* @__PURE__ */ jsx(ScrollReveal, { className: "mb-6 flex h-14 w-14 items-center justify-center rounded-full border border-[#e6e6e6] bg-[rgb(245,246,249)]", children: /* @__PURE__ */ jsx(SmileyIcon, { size: 32, weight: "duotone" }) }),
  /* @__PURE__ */ jsx(
    ScrollReveal,
    {
      as: "h2",
      className: "font-roboto tablet:text-4xl mb-4 text-center text-2xl font-bold",
      children: "Insights from Our Director"
    }
  ),
  /* @__PURE__ */ jsx(
    ScrollReveal,
    {
      as: "p",
      className: "font-roboto tablet:text-lg mb-8 max-w-2xl text-center text-base text-gray-500",
      children: "Hear directly from our Agency Director about how Neutra is shaping the future of digital design and innovation."
    }
  ),
  /* @__PURE__ */ jsxs(ScrollReveal, { className: "mt-4 flex flex-col items-center", children: [
    /* @__PURE__ */ jsx("span", { className: "font-greatvibes mb-2 text-[48px] leading-none text-gray-800", children: "David Johnson" }),
    /* @__PURE__ */ jsx("span", { className: "font-roboto text-xs tracking-widest text-gray-400 uppercase", children: "CEO FOUNDER" })
  ] })
] }) }) });
const StatsSection = () => /* @__PURE__ */ jsx("div", { className: "flex w-full flex-col items-center bg-transparent py-0", children: /* @__PURE__ */ jsx("div", { className: "mx-auto flex w-full max-w-[1200px] flex-col items-center rounded-[32px] bg-white px-4 py-12 shadow-sm tablet:px-16", children: /* @__PURE__ */ jsxs(ScrollReveal, { className: "grid w-full grid-cols-1 gap-6 tablet:grid-cols-4", children: [
  /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center", children: [
    /* @__PURE__ */ jsx("span", { className: "font-roboto tablet:text-4xl text-2xl font-bold text-gray-800", children: "700+" }),
    /* @__PURE__ */ jsx("span", { className: "font-roboto mt-1 text-base tablet:text-lg text-gray-400", children: "Clients" })
  ] }),
  /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center", children: [
    /* @__PURE__ */ jsx("span", { className: "font-roboto tablet:text-4xl text-2xl font-bold text-gray-800", children: "40K" }),
    /* @__PURE__ */ jsx("span", { className: "font-roboto mt-1 text-base tablet:text-lg text-gray-400", children: "Projects" })
  ] }),
  /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center", children: [
    /* @__PURE__ */ jsx("span", { className: "font-roboto tablet:text-4xl text-2xl font-bold text-gray-800", children: "10K+" }),
    /* @__PURE__ */ jsx("span", { className: "font-roboto mt-1 text-base tablet:text-lg text-gray-400", children: "Units sold" })
  ] }),
  /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center", children: [
    /* @__PURE__ */ jsx("span", { className: "font-roboto tablet:text-4xl text-2xl font-bold text-gray-800", children: "99K+" }),
    /* @__PURE__ */ jsx("span", { className: "font-roboto mt-1 text-base tablet:text-lg text-gray-400", children: "Designed" })
  ] })
] }) }) });
const teamMembers = [
  {
    name: "Michael Lee",
    role: "Analyst",
    img: "/images/team/michael-lee.jpg",
    socials: {
      twitter: "#",
      dribbble: "#",
      linkedin: "#"
    }
  },
  {
    name: "Chris Wilson",
    role: "Customer Support",
    img: "/images/team/chris-wilson.jpg",
    socials: {
      twitter: "#",
      dribbble: "#",
      linkedin: "#"
    }
  },
  {
    name: "Emily Brown",
    role: "Designer",
    img: "/images/team/emily-brown.jpg",
    socials: {
      twitter: "#",
      dribbble: "#",
      linkedin: "#"
    }
  },
  {
    name: "Olena Kurcherenko",
    role: "Designer",
    img: "/images/team/olena-kurcherenko.jpg",
    socials: {
      twitter: "#",
      dribbble: "#",
      linkedin: "#"
    }
  },
  {
    name: "Anastasia Solovyova",
    role: "Product Manager",
    img: "/images/team/anastasia-solovyova-team.jpg",
    socials: {
      twitter: "#",
      dribbble: "#",
      linkedin: "#"
    }
  },
  {
    name: "Sarah Smith",
    role: "Project Manager",
    img: "/images/team/sarah-smith.jpg",
    socials: {
      twitter: "#",
      dribbble: "#",
      linkedin: "#"
    }
  },
  {
    name: "David Johnson",
    role: "Founder",
    img: "/images/team/david-johnson.jpg",
    socials: {
      twitter: "#",
      dribbble: "#",
      linkedin: "#"
    }
  }
];
const TeamSection = () => /* @__PURE__ */ jsx("section", { className: "bg-transparent py-20", children: /* @__PURE__ */ jsxs("div", { className: "w-full max-w-7xl px-6", children: [
  /* @__PURE__ */ jsx(
    ScrollReveal,
    {
      as: "h2",
      className: "tablet:text-4xl tablet:font-bold mb-4 w-full text-center text-2xl leading-11 font-normal [word-break:break-word]",
      children: "Meet the Minds Behind Neutra"
    }
  ),
  /* @__PURE__ */ jsx(
    ScrollReveal,
    {
      as: "p",
      className: "tablet:text-lg mx-auto mb-14 max-w-[560px] text-center text-base font-normal text-gray-600",
      children: "Discover the talented individuals who bring Neutra to life, each dedicated to pushing the boundaries of digital design."
    }
  ),
  /* @__PURE__ */ jsx("div", { className: "tablet:grid-cols-3 desktop:grid-cols-4 grid grid-cols-2 justify-items-center [column-gap:16px] [row-gap:32px]", children: teamMembers.map((member) => /* @__PURE__ */ jsxs(
    ScrollReveal,
    {
      className: "flex w-full max-w-[276px] flex-col items-start",
      children: [
        /* @__PURE__ */ jsx("div", { className: "xs:rounded-3xl mb-6 aspect-square w-full max-w-[276px] rounded-2xl sm:rounded-4xl", children: /* @__PURE__ */ jsx(
          "img",
          {
            src: member.img,
            alt: member.name,
            className: "size-full rounded-[inherit] object-cover object-center"
          }
        ) }),
        /* @__PURE__ */ jsx("h5", { className: "mb-1 w-full text-left text-lg font-normal", children: member.name }),
        /* @__PURE__ */ jsx("div", { className: "mb-4 w-full text-left text-sm font-normal text-gray-500", children: member.role }),
        /* @__PURE__ */ jsxs("div", { className: "flex w-full gap-3 text-left text-gray-400", children: [
          /* @__PURE__ */ jsx("a", { href: member.socials.twitter, "aria-label": "Twitter", children: /* @__PURE__ */ jsx(TwitterLogoIcon, { size: 20, weight: "duotone" }) }),
          /* @__PURE__ */ jsx("a", { href: member.socials.dribbble, "aria-label": "Dribbble", children: /* @__PURE__ */ jsx(DribbbleLogoIcon, { size: 20, weight: "duotone" }) }),
          /* @__PURE__ */ jsx("a", { href: member.socials.linkedin, "aria-label": "LinkedIn", children: /* @__PURE__ */ jsx(LinkedinLogoIcon, { size: 20, weight: "duotone" }) })
        ] })
      ]
    },
    member.name
  )) })
] }) });
const faqs = [
  {
    q: "Customized packages?",
    a: "Yes, we understand that every business is unique. We create flexible, customized packages that cater to your specific needs and budget."
  },
  {
    q: "Insights?",
    a: "We rely on data-driven insights and key performance indicators (KPIs) to track progress. You'll receive weekly updates and reports."
  },
  {
    q: "services offered?",
    a: "We provide comprehensive marketing solutions, including SM management, paid advertising, branding, content creation, SEO, email marketing, and full-funnel strategy"
  },
  {
    q: "Expect results?",
    a: "Results vary depending on your goals and the services you select. Typically, clients see noticeable improvements within the first 90 days."
  },
  {
    q: "Pricing?",
    a: "Our pricing depends on the services and scope of work required. We offer flexible payment plans to accommodate businesses of all sizes."
  },
  {
    q: "Get started?",
    a: "Getting started is easy! Simply reach out to us via our contact form or schedule a free consultation. Let's discuss how we can help your brand grow."
  }
];
const FaqsSection = () => /* @__PURE__ */ jsx(ScrollReveal, { children: /* @__PURE__ */ jsxs("section", { className: "tablet:px-6 flex w-full flex-col items-center bg-[#f7f7f9] px-4 py-16", children: [
  /* @__PURE__ */ jsxs("div", { className: "mb-10 text-center", children: [
    /* @__PURE__ */ jsx("h2", { className: "tablet:text-4xl tablet:font-bold mb-2 text-2xl", children: "FAQs" }),
    /* @__PURE__ */ jsx("p", { className: "tablet:text-lg mx-auto max-w-2xl text-base text-gray-500", children: "To make things easier, we've compiled a list of frequently asked questions to address your concerns." })
  ] }),
  /* @__PURE__ */ jsx("div", { className: "tablet:gap-x-10 tablet:gap-y-8 tablet:grid-cols-3 grid w-full max-w-6xl grid-cols-1 gap-x-6 gap-y-6", children: faqs.map((faq, i) => /* @__PURE__ */ jsx(ScrollReveal, { delay: i * 0.05, children: /* @__PURE__ */ jsxs("div", { className: "h-full rounded-xl bg-white p-5 text-left shadow-sm", children: [
    /* @__PURE__ */ jsx("div", { className: "tablet:text-lg mb-1 text-base font-semibold text-gray-900", children: faq.q }),
    /* @__PURE__ */ jsx("div", { className: "tablet:text-base text-sm leading-relaxed text-gray-500", children: faq.a })
  ] }) }, i)) })
] }) });
const SeoHead = ({
  title,
  description,
  keywords,
  canonical,
  ogTitle,
  ogDescription,
  ogImage,
  ogUrl,
  ogType = "website",
  twitterCard = "summary_large_image",
  twitterTitle,
  twitterDescription,
  twitterImage,
  twitterSite = "@pandapatronage",
  robots = "index, follow",
  author = "Panda Patronage",
  structuredData = null,
  additionalMeta = {}
}) => {
  const formattedTitle = title && !title.includes("Panda Patronage") && title !== "Panda Patronage - Digital Marketing & Web Development Agency" ? `${title} | Panda Patronage` : title || "Panda Patronage - Digital Marketing & Web Development Agency";
  const finalOgTitle = ogTitle || formattedTitle;
  const finalOgDescription = ogDescription || description;
  const finalTwitterTitle = twitterTitle || formattedTitle;
  const finalTwitterDescription = twitterDescription || description;
  const finalTwitterImage = twitterImage || ogImage;
  return /* @__PURE__ */ jsxs(Head, { children: [
    /* @__PURE__ */ jsx("title", { children: formattedTitle }),
    description && /* @__PURE__ */ jsx("meta", { name: "description", content: description }),
    keywords && /* @__PURE__ */ jsx("meta", { name: "keywords", content: keywords }),
    author && /* @__PURE__ */ jsx("meta", { name: "author", content: author }),
    /* @__PURE__ */ jsx("meta", { name: "robots", content: robots }),
    canonical && /* @__PURE__ */ jsx("link", { rel: "canonical", href: canonical }),
    /* @__PURE__ */ jsx("meta", { property: "og:title", content: finalOgTitle }),
    finalOgDescription && /* @__PURE__ */ jsx("meta", { property: "og:description", content: finalOgDescription }),
    /* @__PURE__ */ jsx("meta", { property: "og:type", content: ogType }),
    /* @__PURE__ */ jsx("meta", { property: "og:site_name", content: "Panda Patronage" }),
    ogUrl && /* @__PURE__ */ jsx("meta", { property: "og:url", content: ogUrl }),
    ogImage && /* @__PURE__ */ jsx("meta", { property: "og:image", content: ogImage }),
    ogImage && /* @__PURE__ */ jsx("meta", { property: "og:image:alt", content: `${formattedTitle} - Panda Patronage` }),
    /* @__PURE__ */ jsx("meta", { name: "twitter:card", content: twitterCard }),
    /* @__PURE__ */ jsx("meta", { name: "twitter:site", content: twitterSite }),
    /* @__PURE__ */ jsx("meta", { name: "twitter:title", content: finalTwitterTitle }),
    finalTwitterDescription && /* @__PURE__ */ jsx("meta", { name: "twitter:description", content: finalTwitterDescription }),
    finalTwitterImage && /* @__PURE__ */ jsx("meta", { name: "twitter:image", content: finalTwitterImage }),
    Object.entries(additionalMeta).map(([key, value]) => /* @__PURE__ */ jsx("meta", { name: key, content: value }, key)),
    structuredData && /* @__PURE__ */ jsx("script", { type: "application/ld+json", children: JSON.stringify(structuredData) }),
    /* @__PURE__ */ jsx("link", { rel: "icon", type: "image/x-icon", href: "/images/misc/logo.png" }),
    /* @__PURE__ */ jsx("link", { rel: "apple-touch-icon", href: "/images/misc/logo.png" }),
    /* @__PURE__ */ jsx("meta", { name: "viewport", content: "width=device-width, initial-scale=1" }),
    /* @__PURE__ */ jsx("meta", { httpEquiv: "Content-Type", content: "text/html; charset=utf-8" }),
    /* @__PURE__ */ jsx("meta", { name: "language", content: "English" }),
    /* @__PURE__ */ jsx("meta", { name: "revisit-after", content: "7 days" }),
    /* @__PURE__ */ jsx("meta", { name: "distribution", content: "web" }),
    /* @__PURE__ */ jsx("meta", { name: "rating", content: "general" })
  ] });
};
const About = ({ seoData }) => {
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      SeoHead,
      {
        title: seoData?.meta?.title,
        description: seoData?.meta?.description,
        keywords: seoData?.meta?.keywords,
        canonical: seoData?.meta?.canonical,
        ogTitle: seoData?.meta?.["og:title"],
        ogDescription: seoData?.meta?.["og:description"],
        ogImage: seoData?.meta?.["og:image"],
        ogUrl: seoData?.meta?.["og:url"],
        ogType: seoData?.meta?.["og:type"],
        twitterTitle: seoData?.meta?.["twitter:title"],
        twitterDescription: seoData?.meta?.["twitter:description"],
        twitterImage: seoData?.meta?.["twitter:image"],
        structuredData: seoData?.structuredData
      }
    ),
    /* @__PURE__ */ jsxs("div", { className: "px-4", children: [
      /* @__PURE__ */ jsx(VendorSection, {}),
      /* @__PURE__ */ jsx(DirectorSection, {}),
      /* @__PURE__ */ jsx(StatsSection, {}),
      /* @__PURE__ */ jsx(TeamSection, {}),
      /* @__PURE__ */ jsx(FaqsSection, {})
    ] })
  ] });
};
const __vite_glob_0_0 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: About
}, Symbol.toStringTag, { value: "Module" }));
function BlogDetailSection({ article }) {
  if (!article) return null;
  const { image, date, tag, title, content, writer, readTime } = article;
  return /* @__PURE__ */ jsx("section", { className: "flex w-full flex-col items-center px-4 py-12", children: /* @__PURE__ */ jsxs("div", { className: "w-full max-w-3xl", children: [
    /* @__PURE__ */ jsxs(ScrollReveal, { children: [
      /* @__PURE__ */ jsxs(
        Link,
        {
          href: "/blog",
          className: "mb-6 flex items-start text-sm font-medium text-gray-900",
          children: [
            /* @__PURE__ */ jsx(
              ArrowLeftIcon,
              {
                className: "mr-2 inline-block",
                size: 16,
                weight: "thin"
              }
            ),
            " ",
            "All articles"
          ]
        }
      ),
      /* @__PURE__ */ jsx("h1", { className: "tablet:text-5xl mb-4 text-4xl leading-tight font-bold text-gray-900", children: title })
    ] }),
    /* @__PURE__ */ jsxs(ScrollReveal, { className: "tablet:grid-cols-[fit-content(100%)_fit-content(100%)_fit-content(100%)_fit-content(100%)] mb-8 grid w-full grid-cols-[fit-content(100%)_fit-content(100%)] items-center justify-start gap-x-8 gap-y-3 rounded-xl bg-gray-50 px-6 py-5", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex w-full items-center gap-2", children: [
        /* @__PURE__ */ jsx(
          "img",
          {
            src: writer?.image,
            alt: writer?.name,
            className: "h-6 w-6 rounded-full"
          }
        ),
        /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900", children: writer?.name })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex w-full items-center gap-2 text-sm text-gray-600", children: [
        /* @__PURE__ */ jsx(
          ClockIcon,
          {
            weight: "duotone",
            size: 20,
            className: "inline-block"
          }
        ),
        readTime
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex w-full items-center gap-2 text-sm text-gray-600", children: [
        /* @__PURE__ */ jsx(
          CalendarBlankIcon,
          {
            weight: "duotone",
            size: 20,
            className: "inline-block"
          }
        ),
        date
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex w-full items-center gap-2 text-sm text-gray-600", children: [
        /* @__PURE__ */ jsx(
          TagIcon,
          {
            weight: "duotone",
            size: 20,
            className: "inline-block"
          }
        ),
        tag
      ] })
    ] }),
    /* @__PURE__ */ jsx(ScrollReveal, { children: /* @__PURE__ */ jsx(
      "img",
      {
        src: image,
        alt: title,
        className: "mx-auto mb-10 h-[480px] w-full max-w-[762px] rounded-[12px] object-cover",
        style: { aspectRatio: "762/480" }
      }
    ) }),
    /* @__PURE__ */ jsx(ScrollReveal, { className: "space-y-4 text-base leading-relaxed text-gray-700", children: content?.map((p, i) => /* @__PURE__ */ jsx("p", { children: p }, i)) })
  ] }) });
}
const ArticleBlogCard = ({
  image,
  date,
  tag,
  title,
  description,
  href = "#",
  cardClassName = "",
  imgClassName = ""
}) => {
  return /* @__PURE__ */ jsxs(
    "div",
    {
      className: `group relative flex flex-col overflow-hidden rounded-[24px] bg-white transition-transform duration-300 hover:-translate-y-2 ${cardClassName}`,
      children: [
        /* @__PURE__ */ jsx(
          "img",
          {
            src: image,
            alt: title,
            className: `object-cover ${imgClassName}`
          }
        ),
        /* @__PURE__ */ jsxs("div", { className: "flex flex-1 flex-col px-4 py-6", children: [
          /* @__PURE__ */ jsxs("div", { className: "mb-3 flex items-center gap-3", children: [
            /* @__PURE__ */ jsx("span", { className: "text-sm text-gray-500", children: date }),
            /* @__PURE__ */ jsx("span", { className: "rounded-full bg-gray-100 px-3 py-0.5 text-xs font-medium text-gray-900", children: tag })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "font-roboto mb-2 text-[22px] leading-7 font-bold text-gray-900", children: title }),
          /* @__PURE__ */ jsx("div", { className: "font-roboto text-base leading-6 text-gray-600", children: description })
        ] }),
        /* @__PURE__ */ jsx(
          Link,
          {
            href,
            className: "absolute inset-0 z-10",
            tabIndex: "-1",
            "aria-label": title
          }
        )
      ]
    }
  );
};
const BlogGridSection = ({
  articles = [],
  title = "Latest Guides & News Articles",
  subtitle = "Stay informed with the latest guides and news.",
  className = ""
}) => {
  if (!articles.length) return null;
  return /* @__PURE__ */ jsxs(
    "section",
    {
      className: `font-roboto mx-auto w-full max-w-[1200px] px-4 py-20 ${className}`,
      children: [
        /* @__PURE__ */ jsx("div", { className: "mb-10 flex w-full justify-center", children: /* @__PURE__ */ jsx("div", { className: "h-px w-3/4 bg-gray-200" }) }),
        /* @__PURE__ */ jsxs(ScrollReveal, { children: [
          /* @__PURE__ */ jsx("h2", { className: "tablet:text-4xl tablet:font-bold mb-2 text-center text-2xl leading-[56px] text-gray-900", children: title }),
          /* @__PURE__ */ jsx("p", { className: "tablet:text-lg mb-12 text-center text-base text-gray-500", children: subtitle })
        ] }),
        /* @__PURE__ */ jsx("div", { className: "tablet:grid-cols-3 grid grid-cols-1 justify-center gap-8", children: articles.map((article, idx) => /* @__PURE__ */ jsx(ScrollReveal, { children: /* @__PURE__ */ jsx("div", { className: "flex justify-center", children: /* @__PURE__ */ jsx("div", { className: "w-[368px]", children: /* @__PURE__ */ jsx(
          ArticleBlogCard,
          {
            ...article,
            readTime: article.read_time,
            cardClassName: "max-w-[368px] w-full",
            imgClassName: "w-[368px] h-[256.5px] rounded-[24px] object-cover",
            href: `/blog/${encodeURIComponent(article.slug)}`
          }
        ) }) }) }, article.id)) })
      ]
    }
  );
};
function BlogDetailDynamic({
  article,
  seoData,
  latestArticles = []
}) {
  if (!article) {
    return /* @__PURE__ */ jsxs("div", { className: "flex min-h-[60vh] flex-col items-center justify-center", children: [
      /* @__PURE__ */ jsx("h2", { className: "mb-4 text-2xl font-bold", children: "Blog not found" }),
      /* @__PURE__ */ jsx(Link, { href: route("blog"), className: "text-blue-600 underline", children: "Back to all articles" })
    ] });
  }
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      SeoHead,
      {
        title: seoData?.meta?.title,
        description: seoData?.meta?.description,
        keywords: seoData?.meta?.keywords,
        canonical: seoData?.meta?.canonical,
        ogTitle: seoData?.meta?.["og:title"],
        ogDescription: seoData?.meta?.["og:description"],
        ogImage: seoData?.meta?.["og:image"],
        ogUrl: seoData?.meta?.["og:url"],
        ogType: seoData?.meta?.["og:type"],
        twitterTitle: seoData?.meta?.["twitter:title"],
        twitterDescription: seoData?.meta?.["twitter:description"],
        twitterImage: seoData?.meta?.["twitter:image"],
        structuredData: seoData?.structuredData
      }
    ),
    /* @__PURE__ */ jsx(BlogDetailSection, { article }),
    /* @__PURE__ */ jsx(
      BlogGridSection,
      {
        articles: latestArticles,
        title: "Latest Articles",
        subtitle: "Stay informed with the latest guides and news.",
        className: "mt-20 bg-white"
      }
    )
  ] });
}
const __vite_glob_0_1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: BlogDetailDynamic
}, Symbol.toStringTag, { value: "Module" }));
const DiscoverSection = ({ articles = [] }) => {
  const featuredArticles = articles.slice(1, 3);
  return /* @__PURE__ */ jsxs("section", { className: "mx-auto w-full max-w-[1200px] px-4 pt-16", children: [
    /* @__PURE__ */ jsx(ScrollReveal, { children: /* @__PURE__ */ jsxs("h2", { className: "tablet:text-4xl tablet:font-bold mb-12 text-center text-2xl leading-[56px] text-gray-900", children: [
      "Discover articles and",
      /* @__PURE__ */ jsx("br", {}),
      "tutorials to help you build",
      /* @__PURE__ */ jsx("br", {}),
      "better."
    ] }) }),
    /* @__PURE__ */ jsx("div", { className: "flex flex-wrap justify-center gap-8", children: featuredArticles.map((article, idx) => /* @__PURE__ */ jsx(
      ScrollReveal,
      {
        delay: idx * 0.1,
        className: "box-border max-w-[564px] min-w-[280px] flex-1",
        children: /* @__PURE__ */ jsx(
          ArticleBlogCard,
          {
            imgClassName: "rounded-[24px]",
            ...article,
            readTime: article.read_time,
            href: `/blog/${encodeURIComponent(article.slug)}`
          }
        )
      },
      article.id
    )) })
  ] });
};
const Blogs = ({ articles = [], seoData }) => {
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      SeoHead,
      {
        title: seoData?.meta?.title,
        description: seoData?.meta?.description,
        keywords: seoData?.meta?.keywords,
        canonical: seoData?.meta?.canonical,
        ogTitle: seoData?.meta?.["og:title"],
        ogDescription: seoData?.meta?.["og:description"],
        ogImage: seoData?.meta?.["og:image"],
        ogUrl: seoData?.meta?.["og:url"],
        ogType: seoData?.meta?.["og:type"],
        twitterTitle: seoData?.meta?.["twitter:title"],
        twitterDescription: seoData?.meta?.["twitter:description"],
        twitterImage: seoData?.meta?.["twitter:image"],
        structuredData: seoData?.structuredData
      }
    ),
    /* @__PURE__ */ jsxs("div", { className: "bg-white", children: [
      /* @__PURE__ */ jsx(DiscoverSection, { articles }),
      /* @__PURE__ */ jsx(BlogGridSection, { articles })
    ] })
  ] });
};
const __vite_glob_0_2 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Blogs
}, Symbol.toStringTag, { value: "Module" }));
const CasesIntro = () => /* @__PURE__ */ jsx(ScrollReveal, { children: /* @__PURE__ */ jsx("section", { className: "desktop:px-0 bg-white px-2 py-24", children: /* @__PURE__ */ jsxs("div", { className: "tablet:px-0 tablet:justify-between tablet:flex-row tablet:items-start mx-auto mb-14 flex max-w-6xl flex-col items-start justify-center gap-8 px-8", children: [
  /* @__PURE__ */ jsx("h2", { className: "tablet:mb-0 tablet:w-1/2 tablet:text-4xl tablet:font-bold mb-6 w-full text-left text-2xl text-gray-900", children: "Case Studies" }),
  /* @__PURE__ */ jsx("p", { className: "tablet:w-1/2 w-full max-w-[420px] text-left tablet:text-lg text-base text-[rgb(56,56,56)]", children: "Discover the innovative marketing strategies that set Neutra apart, driving success in the digital landscape." })
] }) }) });
const CaseCard = ({ title, img, tags, url }) => /* @__PURE__ */ jsx(
  Link,
  {
    href: url,
    target: "_blank",
    rel: "noopener noreferrer",
    className: "group relative mb-2 flex h-full w-full flex-col items-center rounded-2xl bg-[#f6f7fa] p-6 shadow-sm transition-transform hover:scale-[1.025] focus:ring-2 focus:ring-indigo-500 focus:outline-none",
    style: { minHeight: 420 },
    children: /* @__PURE__ */ jsxs(ScrollReveal, { children: [
      /* @__PURE__ */ jsx(
        "div",
        {
          className: "mb-6 flex w-full items-center justify-center overflow-hidden rounded-2xl",
          style: {
            background: "#23402C"
          },
          children: /* @__PURE__ */ jsx(
            "img",
            {
              src: img,
              alt: title,
              className: "size-full rounded-[inherit] object-cover object-center",
              style: { maxWidth: 532, maxHeight: 322.422 }
            }
          )
        }
      ),
      /* @__PURE__ */ jsxs("div", { className: "flex w-full items-center justify-between text-left", children: [
        /* @__PURE__ */ jsx("h3", { className: "mb-3 text-xl font-semibold text-gray-900", children: title }),
        /* @__PURE__ */ jsx(
          ArrowUpRightIcon,
          {
            className: "tablet:opacity-0 tablet:pointer-events-auto tablet:-translate-x-2 tablet:translate-y-2 tablet:group-hover:opacity-100 tablet:group-hover:translate-x-0 tablet:group-hover:translate-y-0 tablet:transition-all tablet:duration-300 opacity-100",
            size: 32
          }
        )
      ] }),
      /* @__PURE__ */ jsx("div", { className: "flex flex-wrap gap-2", children: tags.map((tag) => /* @__PURE__ */ jsx(
        "span",
        {
          className: "rounded-full border border-gray-300 bg-white px-4 py-1 text-sm font-medium text-gray-700",
          children: tag
        },
        tag
      )) }),
      /* @__PURE__ */ jsx(
        "span",
        {
          className: "absolute inset-0",
          "aria-hidden": "true",
          tabIndex: -1
        }
      )
    ] })
  }
);
const ProjectShowcase$1 = ({ cases: cases2 }) => /* @__PURE__ */ jsx("section", { className: "desktop:px-0 bg-white px-8 pb-24", children: /* @__PURE__ */ jsx("div", { className: "tablet:grid-cols-2 mx-auto grid max-w-6xl grid-cols-1 gap-8", children: cases2.map((item) => /* @__PURE__ */ jsx(CaseCard, { ...item }, item.title)) }) });
const cases = [
  {
    title: "Codify",
    img: "/images/projects/codify.png",
    tags: ["Agency", "Portfolio", "Saas"],
    url: "#"
  },
  {
    title: "Taskify",
    img: "/images/projects/taskify.png",
    tags: ["Business", "AI", "Saas"],
    url: "#"
  },
  {
    title: "Flexify",
    img: "/images/projects/flexify.png",
    tags: ["Saas", "AI", "Business"],
    url: "#"
  },
  {
    title: "Landify",
    img: "/images/projects/landify.png",
    tags: ["Business", "Portfolio", "Landing"],
    url: "#"
  },
  {
    title: "Nexus AI",
    img: "/images/projects/nexus-ai.png",
    tags: ["AI", "Saas", "Business"],
    url: "#"
  },
  {
    title: "Todofusion",
    img: "/images/projects/todofusion.png",
    tags: ["AI", "Business", "Agency"],
    url: "#"
  },
  {
    title: "Brandify",
    img: "/images/projects/taskify.png",
    tags: ["Branding", "Portfolio", "Saas"],
    url: "#"
  },
  {
    title: "MarketPro",
    img: "/images/projects/codify.png",
    tags: ["Marketing", "Business", "AI"],
    url: "#"
  },
  {
    title: "Insightly",
    img: "/images/projects/flexify.png",
    tags: ["Analytics", "Saas", "Business"],
    url: "#"
  },
  {
    title: "Creatify",
    img: "/images/projects/landify.png",
    tags: ["Creative", "Portfolio", "Agency"],
    url: "#"
  },
  {
    title: "Optima",
    img: "/images/projects/nexus-ai.png",
    tags: ["Optimization", "AI", "Saas"],
    url: "#"
  },
  {
    title: "Visionary",
    img: "/images/projects/todofusion.png",
    tags: ["Vision", "Business", "Portfolio"],
    url: "#"
  }
];
const Cases = ({ seoData }) => {
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      SeoHead,
      {
        title: seoData?.meta?.title,
        description: seoData?.meta?.description,
        keywords: seoData?.meta?.keywords,
        canonical: seoData?.meta?.canonical,
        ogTitle: seoData?.meta?.["og:title"],
        ogDescription: seoData?.meta?.["og:description"],
        ogImage: seoData?.meta?.["og:image"],
        ogUrl: seoData?.meta?.["og:url"],
        ogType: seoData?.meta?.["og:type"],
        twitterTitle: seoData?.meta?.["twitter:title"],
        twitterDescription: seoData?.meta?.["twitter:description"],
        twitterImage: seoData?.meta?.["twitter:image"],
        structuredData: seoData?.structuredData
      }
    ),
    /* @__PURE__ */ jsx(CasesIntro, {}),
    /* @__PURE__ */ jsx(ProjectShowcase$1, { cases }),
    /* @__PURE__ */ jsx(FaqsSection, {})
  ] });
};
const __vite_glob_0_3 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Cases
}, Symbol.toStringTag, { value: "Module" }));
const ContactCard = ({
  icon,
  title,
  description,
  address,
  protocol = "",
  cardClassName = ""
}) => /* @__PURE__ */ jsxs(
  ScrollReveal,
  {
    className: `flex flex-1 flex-col rounded-[24px] border border-[#F5F6F9] bg-white p-6 shadow-sm ${cardClassName}`,
    children: [
      /* @__PURE__ */ jsx("div", { className: "mb-4", children: icon }),
      /* @__PURE__ */ jsx("h5", { className: "mb-1 text-lg font-normal text-gray-900", children: title }),
      /* @__PURE__ */ jsx("p", { className: "mb-4 w-full text-sm text-gray-500", children: description }),
      /* @__PURE__ */ jsx("hr", { className: "border-[#e6e6e6] w-full" }),
      protocol ? /* @__PURE__ */ jsx(
        "a",
        {
          href: `${protocol}:${address}`,
          className: "pt-6 text-sm break-words text-gray-900",
          children: address
        }
      ) : /* @__PURE__ */ jsx("address", { className: "pt-6 text-sm break-words text-gray-900 not-italic", children: address })
    ]
  }
);
const Contact = ({ seoData }) => {
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      SeoHead,
      {
        title: seoData?.meta?.title,
        description: seoData?.meta?.description,
        keywords: seoData?.meta?.keywords,
        canonical: seoData?.meta?.canonical,
        ogTitle: seoData?.meta?.["og:title"],
        ogDescription: seoData?.meta?.["og:description"],
        ogImage: seoData?.meta?.["og:image"],
        ogUrl: seoData?.meta?.["og:url"],
        ogType: seoData?.meta?.["og:type"],
        twitterTitle: seoData?.meta?.["twitter:title"],
        twitterDescription: seoData?.meta?.["twitter:description"],
        twitterImage: seoData?.meta?.["twitter:image"],
        structuredData: seoData?.structuredData
      }
    ),
    /* @__PURE__ */ jsxs("div", { className: "flex min-h-screen w-full flex-col items-center bg-white px-2 py-12", children: [
      /* @__PURE__ */ jsxs("div", { className: "mb-10 flex flex-col items-center", children: [
        /* @__PURE__ */ jsx(
          ScrollReveal,
          {
            as: "span",
            className: "mb-4 rounded-full bg-[#F5F6F9] px-4 py-1 text-sm tracking-widest text-gray-500",
            children: "PANDA CONTACT"
          }
        ),
        /* @__PURE__ */ jsx(
          ScrollReveal,
          {
            as: "h1",
            className: "tablet:text-4xl tablet:font-bold mb-4 text-center text-2xl text-gray-900",
            children: "Get in touch with us today!"
          }
        ),
        /* @__PURE__ */ jsx(
          ScrollReveal,
          {
            as: "p",
            className: "tablet:text-lg max-w-xl text-center text-base text-gray-500",
            children: "Contact our sales and support teams for demos, onboarding assistance, or any product inquiries."
          }
        )
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "tablet:flex-row mx-auto mb-12 flex w-full max-w-5xl flex-col gap-6 px-6", children: [
        /* @__PURE__ */ jsx(
          ContactCard,
          {
            icon: /* @__PURE__ */ jsx(
              ChatsIcon,
              {
                size: 32,
                weight: "duotone",
                className: "text-gray-900"
              }
            ),
            title: "Message us",
            description: "Message us using our online chat system for quick and efficient support.",
            address: "<EMAIL>",
            protocol: "mailto",
            cardClassName: "border-[8px] w-full"
          }
        ),
        /* @__PURE__ */ jsx(
          ContactCard,
          {
            icon: /* @__PURE__ */ jsx(
              PhoneCallIcon,
              {
                size: 32,
                weight: "duotone",
                className: "text-gray-900"
              }
            ),
            title: "Call us",
            description: "Let's have a chat – there's nothing quite like talking to another person.",
            address: "+1**************",
            protocol: "tel",
            cardClassName: "border-[8px] w-full"
          }
        ),
        /* @__PURE__ */ jsx(
          ContactCard,
          {
            icon: /* @__PURE__ */ jsx(
              MapPinIcon,
              {
                size: 32,
                weight: "duotone",
                className: "text-gray-900"
              }
            ),
            title: "Address",
            description: "We'd be delighted to welcome you to our Head Office.",
            address: "Montreal, Canada, 110 Notre-Dame St W",
            cardClassName: "border-[8px] w-full"
          }
        )
      ] }),
      /* @__PURE__ */ jsxs(ScrollReveal, { className: "tablet:flex-row flex w-full max-w-6xl flex-col justify-between gap-8 rounded-[24px] border-10 border-[#F5F6F9] bg-white p-8 shadow-sm", children: [
        /* @__PURE__ */ jsxs("div", { className: "tablet:mb-0 mb-6 flex flex-1 flex-col", children: [
          /* @__PURE__ */ jsx("div", { className: "mb-3 flex items-center", children: /* @__PURE__ */ jsx(
            RocketIcon,
            {
              size: 32,
              weight: "duotone",
              className: "mr-3 text-gray-900"
            }
          ) }),
          /* @__PURE__ */ jsx("div", { className: "mb-3 flex items-center", children: /* @__PURE__ */ jsx("span", { className: "text-2xl font-normal text-gray-900", children: "Feel free to send our friendly team a message" }) }),
          /* @__PURE__ */ jsx("div", { className: "mb-3 text-sm text-[#383838]", children: "Message us using our online chat system for quick and efficient support." }),
          /* @__PURE__ */ jsxs("div", { className: "mt-2 flex flex-wrap gap-4 text-sm text-[#313131]", children: [
            /* @__PURE__ */ jsx("span", { children: "Free 7-day trial" }),
            /* @__PURE__ */ jsx("span", { children: "No credit card required" }),
            /* @__PURE__ */ jsx("span", { children: "Cancel anytime" })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("form", { className: "tablet:ml-auto flex w-full max-w-[420px] flex-col gap-4", children: [
          /* @__PURE__ */ jsxs("div", { className: "w-full", children: [
            /* @__PURE__ */ jsx(
              "label",
              {
                className: "mb-1 block text-xs text-[#1f1f1f]",
                htmlFor: "name",
                children: "Name"
              }
            ),
            /* @__PURE__ */ jsx(
              "input",
              {
                id: "name",
                name: "name",
                type: "text",
                placeholder: "Jane Smith",
                className: "w-full rounded-lg bg-[#F5F6F9] px-4 py-2 text-sm text-gray-900 outline-none focus:ring-2 focus:ring-[rgb(255,108,10)]"
              }
            )
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "w-full", children: [
            /* @__PURE__ */ jsx(
              "label",
              {
                className: "mb-1 block text-xs text-[#1f1f1f]",
                htmlFor: "email",
                children: "Email"
              }
            ),
            /* @__PURE__ */ jsx(
              "input",
              {
                id: "email",
                name: "email",
                type: "email",
                placeholder: "<EMAIL>",
                className: "w-full rounded-lg bg-[#F5F6F9] px-4 py-2 text-sm text-gray-900 outline-none focus:ring-2 focus:ring-[rgb(255,108,10)]"
              }
            )
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "w-full", children: [
            /* @__PURE__ */ jsx(
              "label",
              {
                className: "mb-1 block text-xs text-gray-500",
                htmlFor: "location",
                children: "Location"
              }
            ),
            /* @__PURE__ */ jsxs("div", { className: "relative", children: [
              /* @__PURE__ */ jsxs(
                "select",
                {
                  id: "location",
                  name: "location",
                  className: "w-full appearance-none rounded-lg bg-[#F5F6F9] px-4 py-2 pr-10 text-sm text-gray-900 outline-none focus:ring-1 focus:ring-[rgb(255,108,10)]",
                  children: [
                    /* @__PURE__ */ jsx("option", { value: "", children: "Select..." }),
                    /* @__PURE__ */ jsx("option", { value: "montreal", children: "Montreal" }),
                    /* @__PURE__ */ jsx("option", { value: "toronto", children: "Toronto" }),
                    /* @__PURE__ */ jsx("option", { value: "vancouver", children: "Vancouver" })
                  ]
                }
              ),
              /* @__PURE__ */ jsx("span", { className: "pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-400", children: /* @__PURE__ */ jsx(CaretDownIcon, { size: 20 }) })
            ] })
          ] }),
          /* @__PURE__ */ jsx(
            "button",
            {
              type: "submit",
              className: "mt-2 w-full rounded-lg bg-[#1f1f1f] py-2 text-sm font-medium text-white transition hover:cursor-pointer hover:bg-[rgb(56,56,56)]",
              children: "Submit"
            }
          )
        ] })
      ] })
    ] })
  ] });
};
const __vite_glob_0_4 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Contact
}, Symbol.toStringTag, { value: "Module" }));
const HeroSection = () => {
  const [ctaHovered, setCtaHovered] = useState(false);
  const controls = useAnimation();
  useEffect(() => {
    controls.start({
      y: [0, -30, 0],
      transition: {
        repeat: Infinity,
        repeatType: "loop",
        duration: 5,
        ease: "easeInOut"
      }
    });
  }, [controls]);
  return /* @__PURE__ */ jsx(ScrollReveal, { className: "relative flex w-full flex-row flex-nowrap items-center justify-center overflow-visible p-0", children: /* @__PURE__ */ jsx("section", { className: "tablet:p-6 tablet:pb-20 desktop:pb-24 flex h-min w-full items-center justify-center bg-transparent p-4 pb-12", children: /* @__PURE__ */ jsxs("div", { className: "tablet:px-10 tablet:py-20 desktop:p-16 desktop:pt-[100px] desktop:grid-cols-2 tablet:grid-cols-12 relative grid w-full items-center rounded-xl [background-image:linear-gradient(116deg,_rgb(152,59,0)_0%,_rgb(92,36,0)_9%,_rgb(31,31,31)_31%)] px-4 py-14 shadow-lg", children: [
    /* @__PURE__ */ jsxs("div", { className: "tablet:col-span-8 desktop:col-span-1 z-10 flex w-full flex-col justify-center gap-4", children: [
      /* @__PURE__ */ jsxs("h1", { className: "tablet:text-[75px] tablet:w-full h-min w-fit text-left text-5xl leading-[90px] font-thin tracking-[-3.75px] text-white", children: [
        "We're Creative",
        " ",
        /* @__PURE__ */ jsx("br", { className: "tablet:hidden inline" }),
        " Digital Marketing Agency!"
      ] }),
      /* @__PURE__ */ jsx("p", { className: "tablet:text-lg w-full text-xs text-gray-300 [margin-block-end:0px]", children: "We're here to help you see through the eyes of your audience, paving the way for your success without limits." }),
      /* @__PURE__ */ jsxs(
        Link,
        {
          className: `tablet:mt-6 tablet:w-fit relative flex size-fit w-full cursor-pointer items-center overflow-hidden rounded-xl bg-[rgba(255,255,255,0.2)] px-0 py-1.5 text-white`,
          href: "/contact",
          onMouseEnter: () => setCtaHovered(true),
          onMouseLeave: () => setCtaHovered(false),
          children: [
            /* @__PURE__ */ jsxs(
              "div",
              {
                className: `tablet:w-[134.31px] relative flex h-10 w-full items-center justify-center overflow-hidden text-nowrap`,
                children: [
                  /* @__PURE__ */ jsx(
                    motion.span,
                    {
                      initial: { y: "0%" },
                      animate: ctaHovered ? { y: "-100%" } : { y: "0%" },
                      transition: {
                        duration: 0.45,
                        ease: "easeInOut"
                      },
                      className: `absolute inset-0 flex items-center justify-center rounded-[75%] text-sm font-medium text-white`,
                      children: "Schedule a Call"
                    }
                  ),
                  /* @__PURE__ */ jsx(
                    motion.span,
                    {
                      initial: { y: "100%" },
                      animate: ctaHovered ? { y: "0%" } : { y: "100%" },
                      transition: {
                        duration: 0.45,
                        ease: "easeInOut"
                      },
                      className: `absolute inset-0 flex items-center justify-center rounded-[75%] text-sm font-medium text-white`,
                      children: "Schedule a Call"
                    }
                  )
                ]
              }
            ),
            /* @__PURE__ */ jsx("span", { className: "mr-1.5 ml-1 flex size-10 items-center justify-center rounded-lg bg-[#ff7a1a] p-1", children: /* @__PURE__ */ jsx(ArrowRightIcon, { size: 20 }) })
          ]
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "tablet:mt-0 desktop:w-full tablet:col-span-4 desktop:col-span-1 relative mt-10 flex min-h-[370px] items-center justify-center", children: [
      /* @__PURE__ */ jsx(
        motion.img,
        {
          src: "/images/misc/panda-hero.png",
          alt: "Panda with headphones",
          className: "desktop:opacity-100 desktop:block desktop:h-[515px] desktop:w-[601px] absolute -right-16 -bottom-17 z-10 hidden cursor-grab rounded-br-3xl rounded-bl-3xl opacity-0",
          drag: true,
          dragElastic: 1,
          dragConstraints: {
            left: 0,
            right: 0,
            top: 0,
            bottom: 0
          }
        }
      ),
      /* @__PURE__ */ jsx(
        motion.img,
        {
          src: "/images/misc/baby-panda-hero.png",
          alt: "Baby Panda with headphones",
          className: "tablet:opacity-100 tablet:block desktop:hidden absolute right-1 bottom-23 z-10 hidden h-[336px] w-[258px] cursor-grab rounded-br-3xl rounded-bl-3xl opacity-0",
          drag: true,
          dragElastic: 1,
          dragConstraints: {
            left: 0,
            right: 0,
            top: 0,
            bottom: 0
          }
        }
      ),
      /* @__PURE__ */ jsx(
        motion.img,
        {
          src: "/images/misc/hero-left-shape.png",
          alt: "Left shape",
          className: "tablet:hidden desktop:block tablet:-bottom-40 tablet:left-[-60px] tablet:h-[209.625px] tablet:w-[217px] desktop:top-9/10 desktop:left-7 absolute top-[57%] z-10 w-[149px] cursor-grab",
          drag: true,
          dragElastic: 1,
          dragConstraints: {
            left: 0,
            right: 0,
            top: 0,
            bottom: 0
          },
          animate: controls,
          whileDrag: {
            y: 0
          },
          onDragEnd: () => {
            controls.start({
              y: [0, -30, 0],
              transition: {
                repeat: Infinity,
                repeatType: "loop",
                duration: 5,
                ease: "easeInOut"
              }
            });
          }
        }
      ),
      /* @__PURE__ */ jsx(
        motion.img,
        {
          src: "/images/misc/hero-right-shape.png",
          alt: "Right shape",
          className: "desktop:-right-18 tablet:block desktop:bottom-8 tablet:right-[10px] tablet:bottom-[-90px] tablet:size-[180px] desktop:size-[130px] absolute right-[0px] bottom-[-10px] z-10 hidden size-44 cursor-grab brightness-200",
          drag: true,
          dragElastic: 1,
          dragConstraints: {
            left: 0,
            right: 0,
            top: 0,
            bottom: 0
          }
        }
      )
    ] })
  ] }) }) });
};
const WhatSetsUsApart = () => /* @__PURE__ */ jsx("section", { className: "bg-[#f6f7fa] py-16", children: /* @__PURE__ */ jsxs("div", { className: "mx-auto w-full px-4 text-center", children: [
  /* @__PURE__ */ jsxs(ScrollReveal, { children: [
    /* @__PURE__ */ jsx("h2", { className: "tablet:text-4xl tablet:font-bold mb-4 text-2xl text-gray-900", children: "Explore What Sets Us Apart" }),
    /* @__PURE__ */ jsx("p", { className: "tablet:text-lg mx-auto mb-12 max-w-2xl text-base text-gray-700", children: "Explore how we stand out with innovative solutions designed to raise your fulfillment" })
  ] }),
  /* @__PURE__ */ jsxs("div", { className: "desktop:px-0 desktop:grid-cols-3 mx-auto grid w-full grid-cols-1 gap-4 px-8", children: [
    /* @__PURE__ */ jsxs(ScrollReveal, { className: "desktop:h-[260px] desktop:w-[373.328px] flex w-full flex-col items-start rounded-3xl bg-white p-8 text-left", children: [
      /* @__PURE__ */ jsx(
        RocketIcon,
        {
          weight: "duotone",
          color: "#8e8e8e",
          className: "mb-4",
          size: 48
        }
      ),
      /* @__PURE__ */ jsx("h3", { className: "mb-2 text-2xl font-normal text-gray-900", children: "Planning Phase" }),
      /* @__PURE__ */ jsx("p", { className: "text-base text-gray-700", children: "We map out the game plan upfront, deciding what needs to be done and when, so every step is clear and focused." })
    ] }),
    /* @__PURE__ */ jsxs(ScrollReveal, { className: "desktop:h-[260px] desktop:w-[373.328px] flex w-full flex-col items-start rounded-3xl bg-white p-8 text-left", children: [
      /* @__PURE__ */ jsx(
        UsersThreeIcon,
        {
          color: "#8e8e8e",
          weight: "duotone",
          className: "mb-4",
          size: 48
        }
      ),
      /* @__PURE__ */ jsx("h3", { className: "mb-2 text-2xl font-normal text-gray-900", children: "Strategy Blueprint" }),
      /* @__PURE__ */ jsx("p", { className: "text-base text-gray-700", children: "Once the plan is set, we craft a powerful strategy designed to drive your success and fuel your growth." })
    ] }),
    /* @__PURE__ */ jsxs(ScrollReveal, { className: "desktop:h-[260px] desktop:w-[373.328px] flex w-full flex-col items-start rounded-3xl bg-white p-8 text-left", children: [
      /* @__PURE__ */ jsx(
        GearIcon,
        {
          weight: "duotone",
          color: "#8e8e8e",
          className: "mb-4",
          size: 48
        }
      ),
      /* @__PURE__ */ jsx("h3", { className: "mb-2 text-2xl font-normal text-gray-900", children: "Working Process" }),
      /* @__PURE__ */ jsx("p", { className: "text-base text-gray-700", children: "With the plan and strategy in place, our team dive in wholeheartedly to propel your success." })
    ] })
  ] })
] }) });
const services = [
  {
    title: "Branding",
    desc: "Your company’s identity hinges on branding. We assist in shaping a distinct voice and identity.",
    img: "/images/services/branding-service.jpg",
    icons: [
      {
        src: "/images/serviceIcons/adobe-illustrator.png",
        alt: "Adobe Illustrator Logo"
      },
      {
        src: "/images/serviceIcons/adobe-photoshop.png",
        alt: "Adobe Photoshop Logo"
      }
    ]
  },
  {
    title: "Social Media Management",
    desc: "Your audience resides on social media, and we ensure your presence there! Our social media management services maintain your profiles.",
    icons: [
      {
        src: "/images/socialIcons/linkedin.png",
        alt: "LinkedIn Logo"
      },
      {
        src: "/images/socialIcons/facebook.png",
        alt: "Facebook Logo"
      },
      {
        src: "/images/socialIcons/instagram.png",
        alt: "Instagram Logo"
      }
    ]
  },
  {
    title: "Graphic Designing",
    desc: "Our visual design solutions generate powerful imagery, from logos to promotional resources, that deliver your message effectively.",
    img: "/images/services/graphic-designing.png"
  },
  {
    title: "Meta Advertisement",
    desc: "Target the right audience and maximize ROI with our Meta ad campaigns on FB and Instagram.",
    img: "/images/services/meta-advertisement.png"
  },
  {
    title: "Web Development",
    desc: "Your website is your digital storefront—we’re designed to captivate, engage, and drive traffic.",
    icons: [
      {
        src: "/images/techIcons/laravel.png",
        alt: "Laravel Logo"
      },
      {
        src: "/images/techIcons/bootstrap.png",
        alt: "Bootstrap Logo"
      },
      {
        src: "/images/techIcons/tailwind.png",
        alt: "Tailwind CSS Logo"
      },
      {
        src: "/images/techIcons/react.png",
        alt: "React Logo"
      },
      {
        src: "/images/techIcons/php.png",
        alt: "PHP Logo"
      }
    ]
  }
];
const OurServices = () => /* @__PURE__ */ jsx("section", { className: "tablet:py-16 flex items-center justify-center bg-[#f6f7fa]", children: /* @__PURE__ */ jsxs("div", { className: "desktop:w-[1232px] desktop:h-[872px] relative mx-auto w-full overflow-hidden", children: [
  /* @__PURE__ */ jsx(
    "img",
    {
      src: "/images/backgrounds/services-bg.jpg",
      alt: "BG",
      className: "absolute inset-0 z-0 h-full w-full object-cover object-[center_center]",
      style: { filter: "brightness(0.55)" }
    }
  ),
  /* @__PURE__ */ jsxs("div", { className: "tablet:px-12 relative z-10 px-6 py-10", children: [
    /* @__PURE__ */ jsxs("div", { className: "tablet:flex-row tablet:items-center tablet:justify-between mb-10 flex flex-col", children: [
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("span", { className: "mb-4 inline-block rounded-full bg-white/90 px-5 py-1 text-sm font-medium text-gray-900", children: "Our services" }),
        /* @__PURE__ */ jsx("h2", { className: "tablet:text-4xl mb-2 text-2xl leading-tight font-normal text-white", children: "Professional Services That Showcase Our Expertise." })
      ] }),
      /* @__PURE__ */ jsx("p", { className: "tablet:pb-3 tablet:self-end tablet:mt-0 tablet:text-left tablet:text-lg mt-4 max-w-md text-base text-white/90", children: "From creative design to technical solutions, our services define industry excellence." })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "desktop:flex-row desktop:h-[584px] flex h-fit w-full flex-col flex-nowrap gap-4", children: [
      /* @__PURE__ */ jsxs(ScrollReveal, { className: "desktop:flex-[66.447%] desktop:h-[93.838%] flex w-full flex-col rounded-[9px] bg-white/90 p-6 shadow-md", children: [
        /* @__PURE__ */ jsx(
          "img",
          {
            src: services[0].img,
            alt: "Branding",
            className: "mb-4 h-[180px] w-full rounded-xl object-cover"
          }
        ),
        /* @__PURE__ */ jsx("h3", { className: "mb-1 text-lg font-semibold text-gray-900", children: services[0].title }),
        /* @__PURE__ */ jsx("p", { className: "mb-4 text-sm text-gray-700", children: services[0].desc }),
        /* @__PURE__ */ jsx("div", { className: "mt-auto flex items-center gap-2 pl-4", children: services[0].icons.map((icon, i) => /* @__PURE__ */ jsx(
          "img",
          {
            src: icon.src,
            alt: icon.alt,
            className: `-ml-5 w-[44px] rounded-full bg-white p-[2px] z-[1${i}]`
          },
          i
        )) })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "desktop:h-[548px] desktop:w-fit flex w-full flex-col justify-between gap-4", children: [
        /* @__PURE__ */ jsxs(ScrollReveal, { className: "desktop:w-[353.84px] flex min-h-[210px] w-full flex-col rounded-[9px] bg-white/90 p-6 shadow-md", children: [
          /* @__PURE__ */ jsx("h3", { className: "mb-1 text-lg font-semibold text-gray-900", children: services[1].title }),
          /* @__PURE__ */ jsx("p", { className: "mb-4 text-sm text-gray-700", children: services[1].desc }),
          /* @__PURE__ */ jsx("div", { className: "mt-auto flex items-center gap-2 pl-4", children: services[1].icons.map((icon, i) => /* @__PURE__ */ jsx(
            "img",
            {
              src: icon.src,
              alt: icon.alt,
              className: `-ml-5 w-[44px] rounded-full bg-white p-[2px] z-[1${i}]`
            },
            i
          )) })
        ] }),
        /* @__PURE__ */ jsxs(ScrollReveal, { className: "desktop:h-[322px] desktop:w-[353.84px] flex w-full flex-col rounded-[9px] bg-white/90 p-5 shadow-md", children: [
          /* @__PURE__ */ jsx(
            "img",
            {
              src: services[3].img,
              alt: "Meta Advertisement",
              className: "tablet:hidden desktop:block mb-4 h-28 w-full rounded-xl object-cover"
            }
          ),
          /* @__PURE__ */ jsx("h3", { className: "mb-1 text-lg font-semibold text-gray-900", children: services[3].title }),
          /* @__PURE__ */ jsx("p", { className: "mb-4 text-sm text-gray-700", children: services[3].desc })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "desktop:h-[548px] flex w-full flex-col justify-between gap-4", children: [
        /* @__PURE__ */ jsxs(ScrollReveal, { className: "desktop:h-[342px] flex w-full flex-col rounded-[9px] bg-white/90 p-5 shadow-md", children: [
          /* @__PURE__ */ jsx(
            "img",
            {
              src: services[2].img,
              alt: "Graphic Designing",
              className: "mb-4 h-28 w-full rounded-xl object-cover"
            }
          ),
          /* @__PURE__ */ jsx("h3", { className: "mb-1 text-lg font-semibold text-gray-900", children: services[2].title }),
          /* @__PURE__ */ jsx("p", { className: "mb-4 text-sm text-gray-700", children: services[2].desc })
        ] }),
        /* @__PURE__ */ jsxs(ScrollReveal, { className: "desktop:h-[190px] flex w-full flex-col rounded-[9px] bg-white/90 p-5 shadow-md", children: [
          /* @__PURE__ */ jsx("h3", { className: "mb-1 text-lg font-semibold text-gray-900", children: services[4].title }),
          /* @__PURE__ */ jsx("p", { className: "mb-4 text-sm text-gray-700", children: services[4].desc }),
          /* @__PURE__ */ jsx("div", { className: "mt-auto flex items-center gap-2 pl-4", children: services[4].icons.map((icon, i) => /* @__PURE__ */ jsx(
            "img",
            {
              src: icon.src,
              alt: icon.alt,
              className: `-ml-5 w-[44px] rounded-full bg-white p-[2px] z-[1${i}]`
            },
            i
          )) })
        ] })
      ] })
    ] })
  ] })
] }) });
function MotionHeading({ children, className }) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-20% 0px" });
  const [done, setDone] = useState({});
  let charIdx = 0;
  function splitWordsAndSpaces(children2) {
    const result = [];
    React.Children.forEach(children2, (child) => {
      if (typeof child === "string") {
        const regex = /(\S+)|(\s+)/g;
        let match;
        while ((match = regex.exec(child)) !== null) {
          if (match[1]) {
            result.push({ type: "word", value: match[1] });
          } else if (match[2]) {
            result.push({ type: "space", value: match[2] });
          }
        }
      } else if (React.isValidElement(child) && child.type === "br") {
        result.push({ type: "br" });
      } else if (React.isValidElement(child)) {
        result.push(...splitWordsAndSpaces(child.props.children));
      }
    });
    return result;
  }
  const items = splitWordsAndSpaces(children);
  return /* @__PURE__ */ jsx("div", { ref, className, children: /* @__PURE__ */ jsx(AnimatePresence, { children: isInView && /* @__PURE__ */ jsx("span", { className: "inline-block w-full", children: items.map((item, idx) => {
    if (item.type === "br") {
      return /* @__PURE__ */ jsx("br", {}, `br-${idx}`);
    }
    if (item.type === "space") {
      return item.value.split("").map((sp, i) => /* @__PURE__ */ jsx("span", { children: sp === " " ? " " : sp }, `space-${idx}-${i}`));
    }
    if (item.type === "word") {
      const word = item.value;
      return /* @__PURE__ */ jsx("span", { className: "inline-block", children: word.split("").map((char, cidx) => {
        const delay = charIdx * 0.04;
        const thisIdx = charIdx;
        charIdx++;
        return /* @__PURE__ */ jsx(
          motion.span,
          {
            initial: {
              opacity: 0,
              filter: "blur(10px)",
              y: 50,
              scale: 0.8,
              display: "inline-block"
            },
            animate: {
              opacity: 1,
              filter: "blur(0px)",
              y: 0,
              scale: 1
            },
            exit: {
              opacity: 0,
              filter: "blur(10px)",
              y: -20,
              scale: 0.8,
              display: "inline-block"
            },
            transition: {
              duration: 0.8,
              delay,
              ease: [0.6, 0.6, 0, 1]
            },
            className: "animated-char",
            onAnimationComplete: () => setDone((prev) => ({ ...prev, [thisIdx]: true })),
            children: char === " " ? " " : char
          },
          cidx
        );
      }) }, `word-${idx}`);
    }
    return null;
  }) }) }) });
}
const ImpactStatement = () => /* @__PURE__ */ jsxs("section", { className: "xs:py-16 tablet:py-20 relative flex w-full items-center justify-center overflow-visible bg-[#f6f7fa] py-12", children: [
  /* @__PURE__ */ jsxs(ScrollReveal, { children: [
    /* @__PURE__ */ jsx(
      "img",
      {
        src: "/images/misc/star-object.png",
        alt: "Star Object",
        className: "tablet:top-1/2 tablet:left-10 tablet:-translate-y-1/2 tablet:size-16 absolute top-1/5 left-0 size-14"
      }
    ),
    /* @__PURE__ */ jsx(
      "img",
      {
        src: "/images/misc/spring-object.png",
        alt: "Spring Object",
        className: "tablet:block tablet:h-24 tablet:w-24 absolute top-0 right-16 hidden size-24",
        style: { minWidth: 96, minHeight: 96 }
      }
    )
  ] }),
  /* @__PURE__ */ jsx("div", { className: "flex w-full flex-1 items-center justify-center", children: /* @__PURE__ */ jsx(MotionHeading, { className: "xs:mx-6 xs:leading-relaxed tablet:mx-0 text-4xl tablet:leading-snug mx-4 max-w-4xl text-center leading-relaxed font-normal text-gray-900", children: "We have been creating projects that remain relevant today, tomorrow, and for decades to come" }) })
] });
const plans = [
  {
    name: "Part-time",
    price: "$2450",
    features: [
      "Fast delivery & response",
      "Pause / cancel anytime",
      "Dedicated project manager",
      "1 request at the time",
      "SEO Marketing"
    ],
    button: {
      text: "Get Started",
      style: "border border-[#1f1f1f] text-[#1f1f1f] bg-white hover:bg-gray-50"
    },
    highlight: false
  },
  {
    name: "Full-time",
    price: "$3950",
    features: [
      "Fast delivery & response",
      "Pause / cancel anytime",
      "Dedicated project manager",
      "10 request at the time",
      "E-commerce integration"
    ],
    button: {
      text: "Get Started",
      style: "bg-[#1f1f1f] text-white hover:bg-gray-900"
    },
    highlight: false
  },
  {
    name: "Custom",
    price: "$5999",
    features: [
      "Fast delivery & response",
      "Pause / cancel anytime",
      "Dedicated project manager",
      "Unlimited request",
      "E-commerce integration"
    ],
    button: {
      text: "Get Started",
      style: "bg-orange-500 text-white hover:bg-orange-600"
    },
    highlight: true
  }
];
const PricingSection = () => /* @__PURE__ */ jsxs("section", { className: "flex w-full flex-col items-center bg-[#f7f7f9] py-20", children: [
  /* @__PURE__ */ jsxs(ScrollReveal, { className: "mb-12 text-center", children: [
    /* @__PURE__ */ jsx("span", { className: "mb-3 inline-block rounded-full bg-white px-4 py-1 text-sm font-medium tracking-wide text-gray-900", children: "Simple Pricing" }),
    /* @__PURE__ */ jsx("h2", { className: "tablet:font-bold tablet:text-5xl mb-2 text-4xl", children: "Unlock Your Growth" })
  ] }),
  /* @__PURE__ */ jsx("div", { className: "desktop:max-w-5xl desktop:px-0 desktop:flex-row flex w-full flex-col justify-center gap-7 px-8", children: plans.map((plan, idx) => /* @__PURE__ */ jsxs(
    ScrollReveal,
    {
      className: `desktop:max-w-[370px] xs:min-w-[290px] desktop:mx-0 flex w-full flex-1 flex-col rounded-2xl bg-white px-8 py-8 shadow-md ${plan.highlight ? "relative overflow-hidden bg-gradient-to-tl from-[#1f1f1f] to-orange-900 text-white" : "bg-white text-[#1f1f1f]"}`,
      children: [
        /* @__PURE__ */ jsxs("div", { className: "mb-2 flex items-center justify-between", children: [
          /* @__PURE__ */ jsx(
            "span",
            {
              className: `text-2xl ${plan.highlight ? "text-white" : "text-[#1f1f1f]"}`,
              children: plan.name
            }
          ),
          plan.highlight && /* @__PURE__ */ jsx("span", { className: "rounded-full border-[1px] border-orange-500 bg-transparent pt-0.5 pr-3.5 pb-1.5 pl-3 text-xs font-semibold text-orange-500 shadow", children: "Popular" })
        ] }),
        /* @__PURE__ */ jsx(
          "hr",
          {
            className: `my-2 ${plan.highlight ? "border-white/20" : "border-gray-200"}`
          }
        ),
        /* @__PURE__ */ jsx("ul", { className: "mt-2 mb-7 space-y-2", children: plan.features.map((feature, i) => /* @__PURE__ */ jsxs(
          "li",
          {
            className: `flex items-start gap-2 text-sm ${plan.highlight ? "text-orange-100" : "text-gray-700"}`,
            children: [
              /* @__PURE__ */ jsx("span", { className: "mt-1", children: "•" }),
              /* @__PURE__ */ jsx("span", { children: feature })
            ]
          },
          i
        )) }),
        /* @__PURE__ */ jsxs("div", { className: "mt-auto", children: [
          /* @__PURE__ */ jsx(
            "span",
            {
              className: `block text-4xl ${plan.highlight ? "text-white" : "text-[#1f1f1f]"}`,
              children: plan.price
            }
          ),
          /* @__PURE__ */ jsx(
            "span",
            {
              className: `ml-1 text-base ${plan.highlight ? "text-gray-300" : "text-gray-400"}`,
              children: "/month"
            }
          )
        ] }),
        /* @__PURE__ */ jsx(
          "button",
          {
            className: `mt-6 w-full cursor-pointer rounded-lg py-2.5 text-sm font-semibold transition ${plan.button.style} ${plan.highlight ? "shadow-lg" : ""}`,
            children: plan.button.text
          }
        )
      ]
    },
    plan.name
  )) })
] });
const testimonials = [
  {
    name: "Owner",
    company: "Elite Fitness Co.",
    text: "Working with Panda Patronage transformed our brand’s online presence. Their innovative strategies increased our website traffic by 45% in just 3 months!",
    img: "/images/testimonials/elite-fitness-owner.png",
    bg: "#FDF6F3",
    badge: "#fff"
  },
  {
    name: "Manager",
    company: "GreenTech Solutions",
    text: "Panda Patronage took our ad campaigns to the next level. Their full-funnel approach helped us achieve a 200% ROI on our marketing spend.",
    img: "/images/testimonials/greentech-manager.png",
    bg: "#FFF3ED",
    badge: "#F8E6D9"
  },
  {
    name: "CEO",
    company: "Harmony Skin Care",
    text: "We struggled with brand consistency across platforms until their team created a cohesive strategy that boosted customer engagement.",
    img: "/images/testimonials/harmony-ceo.png",
    bg: "#FFF7EB",
    badge: "#F8EED9"
  }
];
const logos = [
  "/images/logos/logo-1.svg",
  "/images/logos/logo-2.svg",
  "/images/logos/logo-3.svg",
  "/images/logos/logo-4.svg",
  "/images/logos/logo-5.svg",
  "/images/logos/logo-6.svg",
  "/images/logos/logo-7.svg",
  "/images/logos/logo-8.svg",
  "/images/logos/logo-9.svg",
  "/images/logos/logo-1.svg",
  "/images/logos/logo-2.svg"
  // Using logo-2 as placeholder for the missing one
];
const TestimonialsSection = () => {
  return /* @__PURE__ */ jsxs("section", { className: "flex flex-col items-center bg-white py-16", children: [
    /* @__PURE__ */ jsxs(ScrollReveal, { className: "mb-10 text-center", children: [
      /* @__PURE__ */ jsx("span", { className: "mb-3 inline-block rounded-full bg-gray-100 px-4 py-1 text-xs font-medium tracking-wide text-gray-700", children: "Testimonials" }),
      /* @__PURE__ */ jsx("h2", { className: "tablet:text-4xl mb-2 text-2xl tablet:font-bold", children: "Our satisfied customers" }),
      /* @__PURE__ */ jsx("p", { className: "mx-auto max-w-xl text-base tablet:text-lg text-gray-500", children: "Panda Patronage empowers teams with seamless Strategies and time-saving Solutions. Discover client success!" })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "desktop:flex-row tablet:flex-nowrap desktop:px-0 tablet:px-8 mb-12 flex w-full flex-col flex-wrap justify-center gap-7 px-4", children: testimonials.map((t, i) => /* @__PURE__ */ jsxs(
      ScrollReveal,
      {
        className: "desktop:mx-0 desktop:max-w-[340px] tablet:min-w-[280px] mx-auto flex w-full flex-1 flex-col items-start gap-4 rounded-2xl px-6 py-7 shadow-md",
        style: { background: t.bg },
        children: [
          /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3.5", children: [
            /* @__PURE__ */ jsx("div", { className: "flex h-12 w-12 items-center justify-center rounded-full bg-white shadow-md ring-2 ring-white", children: /* @__PURE__ */ jsx(
              "img",
              {
                src: t.img,
                alt: t.name,
                width: 42,
                height: 42,
                className: "h-[42px] w-[42px] rounded-full object-cover"
              }
            ) }),
            /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsx("div", { className: "text-base font-semibold text-gray-900", children: t.name }),
              /* @__PURE__ */ jsxs("div", { className: "mt-0.5 text-sm text-gray-400", children: [
                "– ",
                t.company
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "mt-1 text-[1.08rem] leading-relaxed text-gray-800", children: [
            "“",
            t.text,
            "”"
          ] })
        ]
      },
      i
    )) }),
    /* @__PURE__ */ jsxs("div", { className: "relative mx-auto mt-4 h-14 w-full max-w-6xl overflow-hidden", children: [
      /* @__PURE__ */ jsx("div", { className: "animate-logo-slider flex items-center gap-11 will-change-transform", children: [...logos, ...logos].map((logo, i) => /* @__PURE__ */ jsx(
        "img",
        {
          src: logo,
          alt: "logo",
          className: "h-8 opacity-70 grayscale select-none"
        },
        i
      )) }),
      /* @__PURE__ */ jsx(
        "div",
        {
          className: "pointer-events-none absolute top-0 left-0 z-10 h-full w-20",
          style: {
            background: "linear-gradient(90deg, #fff 70%, transparent 100%)"
          }
        }
      ),
      /* @__PURE__ */ jsx(
        "div",
        {
          className: "pointer-events-none absolute top-0 right-0 z-10 h-full w-20",
          style: {
            background: "linear-gradient(270deg, #fff 70%, transparent 100%)"
          }
        }
      )
    ] })
  ] });
};
const projects = [
  {
    title: "Codify",
    img: "/images/projects/codify.png",
    tags: ["Agency", "Portfolio", "Saas"]
  },
  {
    title: "Taskify",
    img: "/images/projects/taskify.png",
    tags: ["Business", "AI", "Saas"]
  },
  {
    title: "Flexify",
    img: "/images/projects/flexify.png",
    tags: ["Saas", "AI", "Business"]
  },
  {
    title: "Landify",
    img: "/images/projects/landify.png",
    tags: ["Business", "Portfolio", "Landing"]
  },
  {
    title: "Nexus AI",
    img: "/images/projects/nexus-ai.png",
    tags: ["AI", "Saas", "Business"]
  },
  {
    title: "Todofusion",
    img: "/images/projects/todofusion.png",
    tags: ["AI", "Business", "Agency"]
  }
];
const ProjectShowcase = () => /* @__PURE__ */ jsxs("section", { className: "tablet:px-0 bg-white px-2 py-24", children: [
  /* @__PURE__ */ jsxs(ScrollReveal, { className: "mx-auto mb-14 max-w-6xl text-center", children: [
    /* @__PURE__ */ jsx("h2", { className: "tablet:text-5xl mb-4 text-2xl tablet:font-bold text-gray-900", children: "Building Digital Excellence with Panda." }),
    /* @__PURE__ */ jsx("p", { className: "mx-auto max-w-2xl text-base tablet:text-lg text-gray-500", children: "Discover the innovative marketing strategies that set Neutra apart, driving success in the digital landscape." })
  ] }),
  /* @__PURE__ */ jsx("div", { className: "desktop:px-0 tablet:grid-cols-2 mx-auto grid w-full max-w-6xl grid-cols-1 gap-8 px-4", children: projects.map((project, idx) => /* @__PURE__ */ jsxs(
    ScrollReveal,
    {
      className: "group mb-2 flex flex-col items-center rounded-2xl bg-[#f6f7fa] p-6 shadow-sm hover:cursor-pointer",
      children: [
        /* @__PURE__ */ jsx(
          "div",
          {
            className: "mb-6 flex w-full items-center justify-center overflow-hidden rounded-2xl",
            style: {
              background: idx % 2 === 0 ? "#23402C" : "linear-gradient(90deg, #6B4EFF 0%, #A259FF 100%)"
            },
            children: /* @__PURE__ */ jsx(
              "img",
              {
                src: project.img,
                alt: project.title,
                className: "size-full rounded-[inherit] object-cover object-center transition-transform duration-600 group-hover:scale-110"
              }
            )
          }
        ),
        /* @__PURE__ */ jsxs("div", { className: "w-full text-left", children: [
          /* @__PURE__ */ jsxs("div", { className: "mb-3 flex items-center justify-between", children: [
            /* @__PURE__ */ jsx("h3", { className: "text-xl font-semibold text-gray-900", children: project.title }),
            /* @__PURE__ */ jsx(
              ArrowUpRightIcon,
              {
                className: "tablet:opacity-0 tablet:pointer-events-auto tablet:-translate-x-2 tablet:translate-y-2 tablet:group-hover:opacity-100 tablet:group-hover:translate-x-0 tablet:group-hover:translate-y-0 tablet:transition-all tablet:duration-300 opacity-100",
                size: 32
              }
            )
          ] }),
          /* @__PURE__ */ jsx("div", { className: "flex flex-wrap gap-2", children: project.tags.map((tag) => /* @__PURE__ */ jsx(
            "span",
            {
              className: "rounded-full border border-gray-300 bg-white px-4 py-1 text-sm font-medium text-gray-700",
              children: tag
            },
            tag
          )) })
        ] })
      ]
    },
    project.title
  )) })
] });
const Home = ({ seoData }) => {
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      SeoHead,
      {
        title: seoData?.meta?.title,
        description: seoData?.meta?.description,
        keywords: seoData?.meta?.keywords,
        canonical: seoData?.meta?.canonical,
        ogTitle: seoData?.meta?.["og:title"],
        ogDescription: seoData?.meta?.["og:description"],
        ogImage: seoData?.meta?.["og:image"],
        ogUrl: seoData?.meta?.["og:url"],
        ogType: seoData?.meta?.["og:type"],
        twitterTitle: seoData?.meta?.["twitter:title"],
        twitterDescription: seoData?.meta?.["twitter:description"],
        twitterImage: seoData?.meta?.["twitter:image"],
        structuredData: seoData?.structuredData
      }
    ),
    /* @__PURE__ */ jsx(HeroSection, {}),
    /* @__PURE__ */ jsx(WhatSetsUsApart, {}),
    /* @__PURE__ */ jsx(OurServices, {}),
    /* @__PURE__ */ jsx(ImpactStatement, {}),
    /* @__PURE__ */ jsx(ProjectShowcase, {}),
    /* @__PURE__ */ jsx(TestimonialsSection, {}),
    /* @__PURE__ */ jsx(PricingSection, {}),
    /* @__PURE__ */ jsx(FaqsSection, {})
  ] });
};
const __vite_glob_0_5 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Home
}, Symbol.toStringTag, { value: "Module" }));
const Licensing = () => {
  return /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-2xl px-4 py-12 text-gray-900", children: [
    /* @__PURE__ */ jsx("h1", { className: "tablet:text-4xl tablet:font-bold mb-2 text-2xl font-normal", children: "Licensing" }),
    /* @__PURE__ */ jsx("p", { className: "mb-10 text-gray-500", children: "September 27, 2023" }),
    /* @__PURE__ */ jsx("h2", { className: "mb-6 text-2xl font-normal", children: "Software Licensing Agreement" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: 'This Software Licensing Agreement ("Agreement") is entered into between [Your Company Name], hereinafter referred to as the "Licensor," and the individual or entity that accepts the terms of this Agreement, hereinafter referred to as the "Licensee."' }),
    /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-normal", children: "License Grant" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: 'Subject to the terms and conditions of this Agreement, Licensor grants Licensee a non-exclusive, non-transferable, and revocable license to use the software ("Software") provided by Licensor. This license allows Licensee to install and use the Software on a single computer or device.' }),
    /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-normal", children: "License Restrictions" }),
    /* @__PURE__ */ jsxs("ol", { className: "mb-8 list-inside list-decimal space-y-1 text-base", children: [
      /* @__PURE__ */ jsx("li", { children: "Licensee may not sublicense, sell, assign, or otherwise transfer the Software to any third party." }),
      /* @__PURE__ */ jsx("li", { children: "Licensee may not modify, reverse engineer, decompile, or disassemble the Software." }),
      /* @__PURE__ */ jsx("li", { children: "Licensee may not distribute, share, or make the Software available over a network, either in part or in its entirety." })
    ] }),
    /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-normal", children: "Intellectual Property" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: "The Software is the intellectual property of Licensor and is protected by copyright and other intellectual property laws. Licensee acknowledges that this Agreement does not grant Licensee any ownership or rights to the Software." }),
    /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-normal", children: "Termination" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: "Licensor reserves the right to terminate this Agreement at any time if Licensee fails to comply with its terms. Upon termination, Licensee must cease using the Software and destroy all copies." }),
    /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-normal", children: "Warranty Disclaimer" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: 'The Software is provided "as is" and without warranties. Licensor disclaims any and all warranties, including but not limited to, the implied warranties of merchantability and fitness for a particular purpose.' }),
    /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-normal", children: "Limitation of Liability" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: "Licensor shall not be liable for any damages, including but not limited to, direct, indirect, special, or consequential damages arising out of the use or inability to use the Software." }),
    /* @__PURE__ */ jsx("h3", { className: "mb-2 text-lg font-normal", children: "Entire Agreement" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: "This Agreement constitutes the entire understanding between the parties and supersedes all previous agreements, understandings, and representations." }),
    /* @__PURE__ */ jsx("p", { className: "mt-10 text-base", children: "By accepting the terms of this Agreement, Licensee acknowledges that they have read, understood, and agree to be bound by its terms." })
  ] });
};
const __vite_glob_0_6 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Licensing
}, Symbol.toStringTag, { value: "Module" }));
const NotFound = () => {
  return /* @__PURE__ */ jsxs("div", { className: "flex min-h-screen flex-col items-center justify-center bg-gray-100 px-4", children: [
    /* @__PURE__ */ jsx("span", { className: "mb-4 rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-[#1f1f1f]", children: "404 error" }),
    /* @__PURE__ */ jsx("h1", { className: "tablet:text-4xl tablet:font-bold mb-2 text-center text-2xl text-gray-900", children: "Page not found" }),
    /* @__PURE__ */ jsx("p", { className: "tablet:text-lg mb-8 max-w-md text-center text-base text-gray-500", children: "We're sorry, but the page you're looking for could not be found. It may have been moved, deleted, or never existed in the first place." }),
    /* @__PURE__ */ jsxs("div", { className: "tablet:flex-row tablet:w-auto flex w-full flex-col gap-3", children: [
      /* @__PURE__ */ jsxs(
        Link,
        {
          href: "/",
          className: "tablet:inline-flex flex w-full items-center justify-center gap-2 rounded-md bg-gray-900 px-4 py-2 text-sm font-semibold text-white shadow transition hover:bg-gray-800",
          children: [
            /* @__PURE__ */ jsx(HouseIcon, { size: 20, weight: "fill" }),
            "Go back home"
          ]
        }
      ),
      /* @__PURE__ */ jsx(
        Link,
        {
          href: "/contact",
          className: "tablet:inline-flex flex items-center justify-center gap-2 rounded-md bg-gray-100 px-5 py-[14px] text-sm font-semibold text-gray-900 transition hover:bg-gray-200",
          children: "Contact"
        }
      )
    ] })
  ] });
};
const __vite_glob_0_7 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: NotFound
}, Symbol.toStringTag, { value: "Module" }));
const PrivacyPolicy = () => {
  return /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-2xl px-4 py-16 text-gray-800", children: [
    /* @__PURE__ */ jsx("h1", { className: "mb-2 text-4xl font-bold", children: "Privacy Policy" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-sm text-gray-500", children: "September 27, 2023" }),
    /* @__PURE__ */ jsxs("section", { className: "mb-8", children: [
      /* @__PURE__ */ jsx("h2", { className: "mb-2 text-xl font-semibold", children: "Introduction" }),
      /* @__PURE__ */ jsx("p", { children: 'Welcome to Codify ("we", "our", or "us"). At Neutra, we are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, and protect your personal data when you use our website, products, or services. By accessing or using our services, you consent to the practices described in this Privacy Policy.' })
    ] }),
    /* @__PURE__ */ jsxs("section", { className: "mb-8", children: [
      /* @__PURE__ */ jsx("h2", { className: "mb-2 text-xl font-semibold", children: "Information We Collect" }),
      /* @__PURE__ */ jsx("p", { className: "mb-2", children: "We may collect various types of information, including:" }),
      /* @__PURE__ */ jsxs("ul", { className: "list-disc space-y-1 pl-6", children: [
        /* @__PURE__ */ jsxs("li", { children: [
          /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Personal Information:" }),
          " Such as your name, email address, and other identifying information you provide when registering or contacting us."
        ] }),
        /* @__PURE__ */ jsxs("li", { children: [
          /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Usage Data:" }),
          " Information about how you interact with our website, products, and services, including IP addresses, device and browser information, and access times."
        ] }),
        /* @__PURE__ */ jsxs("li", { children: [
          /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Cookies:" }),
          " We use cookies to collect information about your preferences, settings, and browsing patterns. You can manage cookie preferences in your browser settings."
        ] }),
        /* @__PURE__ */ jsxs("li", { children: [
          /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Third-party Information:" }),
          " ",
          "Information we may receive from third-party sources or services, which may include social media platforms."
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("section", { className: "mb-8", children: [
      /* @__PURE__ */ jsx("h2", { className: "mb-2 text-xl font-semibold", children: "How We Use Your Information" }),
      /* @__PURE__ */ jsx("p", { className: "mb-2", children: "We use your information for various purposes, including:" }),
      /* @__PURE__ */ jsxs("ul", { className: "list-disc space-y-1 pl-6", children: [
        /* @__PURE__ */ jsx("li", { children: "Providing and improving our products and services." }),
        /* @__PURE__ */ jsx("li", { children: "Responding to your inquiries and support requests." }),
        /* @__PURE__ */ jsx("li", { children: "Sending you promotional materials and updates (if you opt-in)." }),
        /* @__PURE__ */ jsx("li", { children: "Complying with legal obligations and protecting our rights." })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("section", { className: "mb-8", children: [
      /* @__PURE__ */ jsx("h2", { className: "mb-2 text-xl font-semibold", children: "Data Security" }),
      /* @__PURE__ */ jsx("p", { children: "We take the security of your personal information seriously and employ appropriate technical and organizational measures to safeguard it. However, no method of online transmission or storage is 100% secure, and we cannot guarantee absolute security." })
    ] }),
    /* @__PURE__ */ jsxs("section", { className: "mb-8", children: [
      /* @__PURE__ */ jsx("h2", { className: "mb-2 text-xl font-semibold", children: "Third-party Links" }),
      /* @__PURE__ */ jsx("p", { children: "Our website may contain links to third-party sites or services not operated by us. We are not responsible for the privacy practices of such third parties. We encourage you to review their privacy policies." })
    ] }),
    /* @__PURE__ */ jsxs("section", { className: "mb-8", children: [
      /* @__PURE__ */ jsx("h2", { className: "mb-2 text-xl font-semibold", children: "Your Choices" }),
      /* @__PURE__ */ jsx("p", { children: "You can update or delete your personal information by contacting us. You can also opt-out of receiving promotional communications from us." })
    ] }),
    /* @__PURE__ */ jsxs("section", { children: [
      /* @__PURE__ */ jsx("h2", { className: "mb-2 text-xl font-semibold", children: "Changes to This Policy" }),
      /* @__PURE__ */ jsx("p", { children: "We may update this Privacy Policy from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons. We will post the updated Privacy Policy on our website." })
    ] })
  ] });
};
const __vite_glob_0_8 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: PrivacyPolicy
}, Symbol.toStringTag, { value: "Module" }));
const TermsOfUse = () => {
  return /* @__PURE__ */ jsxs("div", { className: "mx-auto max-w-2xl px-4 py-12 text-gray-900", children: [
    /* @__PURE__ */ jsx("h1", { className: "mb-2 text-4xl font-bold", children: "Terms of Use" }),
    /* @__PURE__ */ jsx("p", { className: "mb-10 text-gray-500", children: "September 27, 2023" }),
    /* @__PURE__ */ jsx("p", { className: "mb-4 text-base", children: 'Please read these Terms of Use ("Agreement") carefully before using Neutra ("us," "we," or "our"). This Agreement sets forth the legally binding terms and conditions for your use of the Neutra website and any of its related services.' }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: "By accessing or using Neutra in any manner, including, but not limited to, visiting or browsing Neutra or contributing content or other materials to Neutra, you agree to be bound by this Agreement." }),
    /* @__PURE__ */ jsx("h3", { className: "mt-8 mb-2 text-lg font-semibold", children: "Intellectual Property" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: "Neutra and its original content, features, and functionality are owned by Neutra and are protected by international copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws." }),
    /* @__PURE__ */ jsx("h3", { className: "mt-8 mb-2 text-lg font-semibold", children: "Termination" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: "We may terminate your access to Neutra, without cause or notice, which may result in the forfeiture and destruction of all information associated with your account. All provisions of this Agreement that by their nature should survive termination shall survive termination, including, without limitation, ownership provisions, warranty disclaimers, indemnity, and limitations of liability." }),
    /* @__PURE__ */ jsx("h3", { className: "mt-8 mb-2 text-lg font-semibold", children: "Links to Other Websites" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: "Our website may contain links to third-party websites. These links are provided solely as a convenience to you. By linking to these websites, we do not create or have an affiliation with, or sponsor such third-party websites. The inclusion of links within our website does not constitute any endorsement, guarantee, warranty, or recommendation of such third-party websites. Codify has no control over the legal documents and privacy practices of third-party websites. As such, you access any such third-party websites at your own risk." }),
    /* @__PURE__ */ jsx("h3", { className: "mt-8 mb-2 text-lg font-semibold", children: "Changes to This Agreement" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: "We reserve the right to modify this Agreement at any time. You should check this Agreement periodically for changes. By using Neutra after we post any changes to this Agreement, you agree to accept those changes, whether or not you have reviewed them." }),
    /* @__PURE__ */ jsx("h3", { className: "mt-8 mb-2 text-lg font-semibold", children: "Contact Us" }),
    /* @__PURE__ */ jsx("p", { className: "mb-8 text-base", children: "If you have any questions about this Agreement, please contact <NAME_EMAIL>." })
  ] });
};
const __vite_glob_0_9 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: TermsOfUse
}, Symbol.toStringTag, { value: "Module" }));
const DesktopHeader = ({ activeLink }) => {
  const dotVariants = {
    rest: { left: "-10px", opacity: 0 },
    hover: {
      left: "50%",
      x: "-50%",
      opacity: 1,
      transition: { type: "spring", stiffness: 200, damping: 20 }
    }
  };
  const links = [
    { label: "Home", href: route("home") },
    { label: "Cases", href: route("cases") },
    { label: "About", href: route("about") },
    { label: "Blog", href: route("blog") },
    { label: "Contact", href: route("contact") }
  ];
  const [scrolled, setScrolled] = useState(false);
  useEffect(() => {
    const onScroll = () => setScrolled(window.scrollY > 0);
    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);
  const [ctaHovered, setCtaHovered] = useState(false);
  return /* @__PURE__ */ jsxs(
    "header",
    {
      className: `desktop:flex hidden items-center overflow-x-hidden ${scrolled ? "fixed left-1/2 z-50 mt-3 w-fit -translate-x-1/2 items-center gap-1 rounded-[16px] bg-[rgba(26,26,26,0.5)] p-2 backdrop-blur-[5px]" : "relative mx-auto h-min w-full max-w-[1200px] justify-between bg-transparent p-6"}`,
      children: [
        /* @__PURE__ */ jsx(
          Link,
          {
            href: route("home"),
            className: `flex-shrink-0 ${scrolled ? "p-1 pl-0" : "p-2"}`,
            children: /* @__PURE__ */ jsx(
              "img",
              {
                src: "/images/misc/logo.png",
                alt: "Panda Patronage Logo",
                className: ` ${scrolled ? "size-[42px] object-contain" : "size-5 object-contain"} `
              }
            )
          }
        ),
        /* @__PURE__ */ jsxs(
          "div",
          {
            className: `flex items-center ${scrolled ? "items-stretch gap-1" : "gap-4"}`,
            children: [
              /* @__PURE__ */ jsx(
                "nav",
                {
                  className: `flex flex-shrink-0 ${scrolled ? "items-stretch rounded-xl bg-[#1f1f1f] p-1 pb-0" : "items-center"} `,
                  children: /* @__PURE__ */ jsx(
                    "ul",
                    {
                      className: `m-0 flex list-none items-center ${scrolled ? "gap-0" : "gap-1"} p-0`,
                      children: links.map(({ label, href }) => {
                        const isActive = label === activeLink;
                        const hasValidActive = links.some(
                          (link) => link.label === activeLink
                        );
                        if (isActive && hasValidActive) {
                          return /* @__PURE__ */ jsx(
                            "li",
                            {
                              className: `relative px-4 ${scrolled ? "pt-1 pb-2" : "py-2"} `,
                              children: /* @__PURE__ */ jsxs(
                                Link,
                                {
                                  href,
                                  className: `relative cursor-[unset] text-sm font-medium ${scrolled ? "text-white" : "text-[#1f1f1f]"} `,
                                  children: [
                                    label,
                                    /* @__PURE__ */ jsx(
                                      "span",
                                      {
                                        className: `absolute bottom-[-5px] left-1/2 size-[3px] -translate-x-1/2 transform rounded-full ${scrolled ? "bg-white" : "bg-[#1f1f1f]"} `
                                      }
                                    )
                                  ]
                                }
                              )
                            },
                            label
                          );
                        } else {
                          return /* @__PURE__ */ jsx(
                            motion.li,
                            {
                              className: `relative px-4 ${scrolled ? "pt-1 pb-2" : "py-2"} `,
                              initial: "rest",
                              whileHover: "hover",
                              animate: "rest",
                              children: /* @__PURE__ */ jsxs(
                                Link,
                                {
                                  href,
                                  className: `relative text-sm font-medium ${scrolled ? "text-white" : "text-[#1f1f1f]"} `,
                                  children: [
                                    label,
                                    /* @__PURE__ */ jsx(
                                      motion.span,
                                      {
                                        className: `absolute right-0 bottom-[-5px] left-0 size-[3px] rounded-full ${scrolled ? "bg-white" : "bg-[#8e8e8e]"} `,
                                        variants: dotVariants
                                      }
                                    )
                                  ]
                                }
                              )
                            },
                            label
                          );
                        }
                      })
                    }
                  )
                }
              ),
              /* @__PURE__ */ jsx(
                "button",
                {
                  onMouseEnter: () => setCtaHovered(true),
                  onMouseLeave: () => setCtaHovered(false),
                  className: `relative cursor-pointer overflow-hidden ${scrolled ? "flex flex-col items-center rounded-xl bg-white px-5 py-3.5 text-[#1f1f1f]" : "rounded-lg bg-gray-900 px-0 py-1.5 text-white"} `,
                  children: /* @__PURE__ */ jsxs(
                    "div",
                    {
                      className: `relative flex items-center justify-center overflow-hidden ${scrolled ? "h-5 w-[57.981px] bg-white" : "h-6 w-[89.9px] bg-gray-900"} `,
                      children: [
                        /* @__PURE__ */ jsx(
                          motion.span,
                          {
                            initial: { y: "0%" },
                            animate: ctaHovered ? { y: "-100%" } : { y: "0%" },
                            transition: { duration: 0.45, ease: "easeInOut" },
                            className: `absolute inset-0 flex items-center justify-center rounded-[75%] text-sm font-medium ${scrolled ? "text-[#1f1f1f]" : "text-white"} `,
                            children: "Let's Talk"
                          }
                        ),
                        /* @__PURE__ */ jsx(
                          motion.span,
                          {
                            initial: { y: "100%" },
                            animate: ctaHovered ? { y: "0%" } : { y: "100%" },
                            transition: { duration: 0.45, ease: "easeInOut" },
                            className: `absolute inset-0 flex items-center justify-center rounded-[75%] text-sm font-medium ${scrolled ? "text-[#1f1f1f]" : "text-white"} `,
                            children: "Let's Talk"
                          }
                        )
                      ]
                    }
                  )
                }
              )
            ]
          }
        )
      ]
    }
  );
};
const MobileHeader = ({ activeLink }) => {
  const [scrolled, setScrolled] = useState(false);
  useEffect(() => {
    const onScroll = () => setScrolled(window.scrollY > 0);
    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);
  const [ctaHovered, setCtaHovered] = useState(false);
  const [isOpen, toggleOpen] = useCycle(false, true);
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);
  const menuItems = [
    { label: "Home", href: route("home") },
    { label: "Cases", href: route("cases") },
    { label: "About", href: route("about") },
    { label: "Blog", href: route("blog") },
    { label: "Contact", href: route("contact") }
  ];
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    isOpen && !scrolled && /* @__PURE__ */ jsx(
      motion.div,
      {
        initial: { opacity: 0 },
        animate: { opacity: 1, pointerEvents: "auto" },
        exit: { opacity: 0, pointerEvents: "none" },
        transition: { duration: 0.2 },
        className: "fixed right-0 left-0 z-30",
        style: {
          top: 84,
          // height of navbar in normal flow
          height: "calc(100vh - 84px)",
          pointerEvents: isOpen ? "auto" : "none"
        },
        "aria-hidden": !isOpen,
        children: /* @__PURE__ */ jsx(
          motion.div,
          {
            initial: { opacity: 0 },
            animate: { opacity: 1 },
            exit: { opacity: 0 },
            transition: { duration: 0.2 },
            className: "h-full w-full bg-white/60 backdrop-blur-sm"
          }
        )
      }
    ),
    isOpen && scrolled && /* @__PURE__ */ jsx(
      motion.div,
      {
        initial: { opacity: 0 },
        animate: { opacity: 1, pointerEvents: "auto" },
        exit: { opacity: 0, pointerEvents: "none" },
        transition: { duration: 0.2 },
        className: "fixed inset-0 z-30",
        style: { pointerEvents: isOpen ? "auto" : "none" },
        "aria-hidden": !isOpen,
        children: /* @__PURE__ */ jsx(
          motion.div,
          {
            initial: { opacity: 0 },
            animate: { opacity: 1 },
            exit: { opacity: 0 },
            transition: { duration: 0.2 },
            className: "absolute inset-0 h-full w-full bg-white/60 backdrop-blur-sm"
          }
        )
      }
    ),
    /* @__PURE__ */ jsxs(
      "header",
      {
        className: `desktop:hidden block w-full overflow-x-hidden ${scrolled ? "fixed top-0 left-0 z-50 w-full" : "relative"}`,
        children: [
          /* @__PURE__ */ jsxs(
            "div",
            {
              className: `flex items-center justify-between ${scrolled ? "mx-auto mt-3 h-[58px] w-2/3 gap-1 rounded-[16px] bg-[rgba(26,26,26,0.5)] p-2 backdrop-blur-[5px]" : "h-[84px] w-full bg-transparent p-4"} `,
              children: [
                /* @__PURE__ */ jsx(Link, { href: route("home"), className: "flex-shrink-0", children: /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/images/misc/logo.png",
                    alt: "Panda Patronage Logo",
                    className: ` ${scrolled ? "size-[42px] object-contain" : "size-5 object-contain"} `
                  }
                ) }),
                /* @__PURE__ */ jsxs(
                  "div",
                  {
                    className: `flex items-center ${scrolled ? "gap-2" : "gap-8"}`,
                    children: [
                      /* @__PURE__ */ jsx(
                        "button",
                        {
                          onMouseEnter: () => setCtaHovered(true),
                          onMouseLeave: () => setCtaHovered(false),
                          className: `relative cursor-pointer overflow-hidden rounded-xl ${scrolled ? "bg-white px-5 py-3.5 text-[#1f1f1f]" : "bg-gray-900 px-0 py-1.5 text-white"} `,
                          children: /* @__PURE__ */ jsxs(
                            "div",
                            {
                              className: `relative flex items-center justify-center overflow-hidden ${scrolled ? "h-5 w-[57.981px] bg-white" : "h-6 w-[89.9px] bg-gray-900"} `,
                              children: [
                                /* @__PURE__ */ jsx(
                                  motion.span,
                                  {
                                    initial: { y: "0%" },
                                    animate: ctaHovered ? { y: "-100%" } : { y: "0%" },
                                    transition: {
                                      duration: 0.45,
                                      ease: "easeInOut"
                                    },
                                    className: `absolute inset-0 flex items-center justify-center rounded-[75%] text-sm font-medium ${scrolled ? "text-[#1f1f1f]" : "text-white"} `,
                                    children: "Let's Talk"
                                  }
                                ),
                                /* @__PURE__ */ jsx(
                                  motion.span,
                                  {
                                    initial: { y: "100%" },
                                    animate: ctaHovered ? { y: "0%" } : { y: "100%" },
                                    transition: {
                                      duration: 0.45,
                                      ease: "easeInOut"
                                    },
                                    className: `absolute inset-0 flex items-center justify-center rounded-[75%] text-sm font-medium ${scrolled ? "text-[#1f1f1f]" : "text-white"} `,
                                    children: "Let's Talk"
                                  }
                                )
                              ]
                            }
                          )
                        }
                      ),
                      /* @__PURE__ */ jsx(
                        "button",
                        {
                          onClick: () => toggleOpen(),
                          className: `relative z-50 flex cursor-pointer items-center justify-center ${scrolled ? "rounded-lg bg-white p-2" : ""}`,
                          children: /* @__PURE__ */ jsxs("svg", { width: "30", height: "30", viewBox: "0 0 30 30", children: [
                            /* @__PURE__ */ jsx(
                              motion.line,
                              {
                                initial: {
                                  x1: 4,
                                  y1: 7,
                                  x2: 24,
                                  y2: 7,
                                  rotate: 0
                                },
                                animate: isOpen ? {
                                  y1: 15,
                                  y2: 15,
                                  rotate: 45,
                                  x1: 4,
                                  x2: 24
                                } : {
                                  y1: 7,
                                  y2: 7,
                                  rotate: 0,
                                  x1: 4,
                                  x2: 24
                                },
                                stroke: "hsl(0, 0%, 18%)",
                                strokeWidth: "3",
                                strokeLinecap: "round",
                                transition: {
                                  duration: 0.3,
                                  ease: "easeInOut"
                                },
                                style: { originX: 0.5, originY: 0.5 }
                              }
                            ),
                            /* @__PURE__ */ jsx(
                              motion.line,
                              {
                                initial: {
                                  x1: 4,
                                  y1: 15,
                                  x2: 24,
                                  y2: 15,
                                  opacity: 1
                                },
                                animate: isOpen ? { opacity: 0 } : {
                                  opacity: 1,
                                  x1: 4,
                                  x2: 24,
                                  y1: 15,
                                  y2: 15
                                },
                                stroke: "hsl(0, 0%, 18%)",
                                strokeWidth: "3",
                                strokeLinecap: "round",
                                transition: {
                                  duration: 0.2,
                                  ease: "easeInOut"
                                }
                              }
                            ),
                            /* @__PURE__ */ jsx(
                              motion.line,
                              {
                                initial: {
                                  x1: 4,
                                  y1: 23,
                                  x2: 24,
                                  y2: 23,
                                  rotate: 0
                                },
                                animate: isOpen ? {
                                  y1: 15,
                                  y2: 15,
                                  rotate: -45,
                                  x1: 4,
                                  x2: 24
                                } : {
                                  y1: 23,
                                  y2: 23,
                                  rotate: 0,
                                  x1: 4,
                                  x2: 24
                                },
                                stroke: "hsl(0, 0%, 18%)",
                                strokeWidth: "3",
                                strokeLinecap: "round",
                                transition: {
                                  duration: 0.3,
                                  ease: "easeInOut"
                                },
                                style: { originX: 0.5, originY: 0.5 }
                              }
                            )
                          ] })
                        }
                      )
                    ]
                  }
                )
              ]
            }
          ),
          /* @__PURE__ */ jsx(
            motion.div,
            {
              initial: { height: 0, opacity: 0 },
              animate: isOpen ? { height: "auto", opacity: 1, display: "block" } : { height: 0, opacity: 0, display: "none" },
              transition: { duration: 0.4, ease: "easeInOut" },
              className: "fixed right-0 left-0 z-40 mx-2 mt-3 overflow-hidden rounded-2xl bg-neutral-900 p-6 shadow-lg",
              style: { top: scrolled ? 70 : 100 },
              children: /* @__PURE__ */ jsx("nav", { children: /* @__PURE__ */ jsx("ul", { className: "flex flex-col gap-6", children: menuItems.map((item) => /* @__PURE__ */ jsx("li", { className: "group relative", children: /* @__PURE__ */ jsxs(
                Link,
                {
                  href: item.href,
                  onClick: () => toggleOpen(),
                  className: `flex items-center gap-2 text-sm font-medium transition-colors duration-200 ${activeLink === item.label ? "text-white" : "text-neutral-300 hover:text-white"}`,
                  children: [
                    /* @__PURE__ */ jsx(
                      "span",
                      {
                        className: `mr-0 mb-[2.4px] size-[3px] rounded-full bg-white transition-all duration-200 ${activeLink === item.label ? "opacity-100" : "opacity-0 group-hover:opacity-100"}`
                      }
                    ),
                    item.label
                  ]
                }
              ) }, item.label)) }) })
            }
          )
        ]
      }
    )
  ] });
};
const Footer = () => {
  return /* @__PURE__ */ jsxs(ScrollReveal, { className: "tablet:px-6 tablet:pt-12 m-4 flex flex-col justify-between rounded-2xl p-8 font-sans text-white [background:linear-gradient(-25deg,black_55%,#7c2d12_100%)]", children: [
    /* @__PURE__ */ jsxs("div", { className: "tablet:grid-cols-3 tablet:justify-between desktop:gap-12 desktop:pb-12 mx-auto grid w-full max-w-6xl grid-cols-1 gap-8 pb-8", children: [
      /* @__PURE__ */ jsxs("div", { className: "tablet:col-span-1 flex flex-col", children: [
        /* @__PURE__ */ jsx(
          "img",
          {
            src: "/images/misc/footer-logo.png",
            alt: "Logo",
            className: "mb-3 h-10 w-32"
          }
        ),
        /* @__PURE__ */ jsx("p", { className: "max-w-sm text-sm leading-relaxed text-white", children: "Get in touch to find out more about digital experiences to effectively reach and engage customers and target audiences." })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "tablet:flex tablet:flex-row tablet:justify-end tablet:gap-16 tablet:col-span-2 desktop:gap-20 grid grid-cols-2 gap-8", children: [
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h3", { className: "mb-2 text-base font-semibold text-[#a7a7a7]", children: "Company" }),
          /* @__PURE__ */ jsxs("ul", { className: "space-y-1", children: [
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
              Link,
              {
                href: "/",
                className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
                children: "Home"
              }
            ) }),
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
              Link,
              {
                href: "/about",
                className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
                children: "About"
              }
            ) }),
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
              Link,
              {
                href: "/contact",
                className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
                children: "Contact"
              }
            ) }),
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
              Link,
              {
                href: "/cases",
                className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
                children: "Cases"
              }
            ) }),
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
              Link,
              {
                href: "/blog",
                className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
                children: "Article"
              }
            ) })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h3", { className: "mb-2 text-base font-semibold text-[#a7a7a7]", children: "Legal" }),
          /* @__PURE__ */ jsxs("ul", { className: "space-y-1", children: [
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
              Link,
              {
                href: "/privacy-policy",
                className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
                children: "Privacy Policy"
              }
            ) }),
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
              Link,
              {
                href: "/licensing",
                className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
                children: "Licensing"
              }
            ) }),
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(
              Link,
              {
                href: "/terms-of-use",
                className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
                children: "Terms of Use"
              }
            ) })
          ] })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "mt-auto w-full", children: /* @__PURE__ */ jsxs("div", { className: "tablet:flex-row desktop:gap-0 desktop:justify-between flex w-full flex-col items-start justify-between gap-8 border-t border-gray-800 px-2 py-3", children: [
      /* @__PURE__ */ jsx("p", { className: "text-left text-sm text-gray-500", children: "© Panda-Patronage 2025" }),
      /* @__PURE__ */ jsxs("div", { className: "flex space-x-4", children: [
        /* @__PURE__ */ jsx(
          "a",
          {
            href: "#",
            className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
            children: /* @__PURE__ */ jsx(TwitterLogoIcon, { size: 20, weight: "duotone" })
          }
        ),
        /* @__PURE__ */ jsx(
          "a",
          {
            href: "#",
            className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
            children: /* @__PURE__ */ jsx(DribbbleLogoIcon, { size: 20, weight: "duotone" })
          }
        ),
        /* @__PURE__ */ jsx(
          "a",
          {
            href: "#",
            className: "text-sm font-medium text-white transition-colors duration-300 hover:text-[rgb(255,108,10)]",
            children: /* @__PURE__ */ jsx(LinkedinLogoIcon, { size: 20, weight: "duotone" })
          }
        )
      ] })
    ] }) })
  ] });
};
const SkipLink = () => {
  return /* @__PURE__ */ jsx(
    "a",
    {
      href: "#main-content",
      className: "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:bg-blue-600 focus:text-white focus:px-4 focus:py-2 focus:rounded focus:outline-none focus:ring-2 focus:ring-blue-500",
      children: "Skip to main content"
    }
  );
};
const navLinks = [
  { label: "Home", href: route("home") },
  { label: "Cases", href: route("cases") },
  { label: "About", href: route("about") },
  { label: "Blog", href: route("blog") },
  { label: "Contact", href: route("contact") }
];
const Layout = ({ children }) => {
  const { url } = usePage();
  const activeLink = navLinks.find(
    (link) => link.href === route("home") ? url === route("home") : url.startsWith(link.href)
  )?.label;
  const isHome = url === route("home");
  useEffect(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth"
    });
  }, [url]);
  return /* @__PURE__ */ jsxs(
    "div",
    {
      className: `relative ${isHome ? "bg-[rgb(245,246,249)]" : "bg-white"}`,
      children: [
        /* @__PURE__ */ jsx(SkipLink, {}),
        /* @__PURE__ */ jsx(DesktopHeader, { activeLink }),
        /* @__PURE__ */ jsx(MobileHeader, { activeLink }),
        /* @__PURE__ */ jsx("main", { id: "main-content", role: "main", children }),
        /* @__PURE__ */ jsx(Footer, {})
      ]
    }
  );
};
function render(page) {
  return createInertiaApp({
    page,
    render: renderToString,
    resolve: (name) => {
      const pages = /* @__PURE__ */ Object.assign({ "./pages/About.jsx": __vite_glob_0_0, "./pages/BlogDetailDynamic.jsx": __vite_glob_0_1, "./pages/Blogs.jsx": __vite_glob_0_2, "./pages/Cases.jsx": __vite_glob_0_3, "./pages/Contact.jsx": __vite_glob_0_4, "./pages/Home.jsx": __vite_glob_0_5, "./pages/Licensing.jsx": __vite_glob_0_6, "./pages/NotFound.jsx": __vite_glob_0_7, "./pages/PrivacyPolicy.jsx": __vite_glob_0_8, "./pages/TermsOfUse.jsx": __vite_glob_0_9 });
      const page2 = pages[`./pages/${name}.jsx`];
      page2.default.layout = page2.default.layout || ((page3) => /* @__PURE__ */ jsx(Layout, { children: page3 }));
      return page2;
    },
    setup: ({ App, props }) => /* @__PURE__ */ jsx(App, { ...props })
  });
}
export {
  render as default
};
