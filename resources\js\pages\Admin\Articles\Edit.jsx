import { useState } from "react";
import { Head, useForm, router } from "@inertiajs/react";
import { ArrowLeft } from "@phosphor-icons/react";
import { Editor } from "@tinymce/tinymce-react";

const Edit = ({ article }) => {
    const [imagePreview, setImagePreview] = useState(article.image);
    const { data, setData, post, processing, errors } = useForm({
        title: article.title || "",
        tag: article.tag || "",
        content:
            typeof article.content === "string"
                ? article.content
                : Array.isArray(article.content)
                  ? article.content.map((block) => block.content).join("\n\n")
                  : "",
        writer: article.writer || { name: "", avatar: "", bio: "" },
        read_time: article.read_time || "",
        image: null,
        is_featured: article.is_featured || false,
        _method: "PUT",
    });

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setData("image", file);
            const reader = new FileReader();
            reader.onload = (e) => setImagePreview(e.target.result);
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post(`/admin/articles/${article.id}`);
    };

    return (
        <>
            <Head title={`Edit Article - ${article.title}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center">
                    <button
                        onClick={() => router.visit("/admin/articles")}
                        className="mr-4 p-2 text-gray-400 hover:text-gray-600"
                    >
                        <ArrowLeft className="h-5 w-5" />
                    </button>
                    <div>
                        <h1 className="text-2xl font-semibold text-gray-900">
                            Edit Article
                        </h1>
                        <p className="mt-1 text-sm text-gray-600">
                            Update article information
                        </p>
                    </div>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="bg-white px-4 py-5 shadow sm:rounded-lg sm:p-6">
                        <div className="md:grid md:grid-cols-3 md:gap-6">
                            <div className="md:col-span-1">
                                <h3 className="text-lg leading-6 font-medium text-gray-900">
                                    Article Information
                                </h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    Basic information about the article
                                </p>
                            </div>
                            <div className="mt-5 md:col-span-2 md:mt-0">
                                <div className="grid grid-cols-6 gap-6">
                                    <div className="col-span-6">
                                        <label
                                            htmlFor="title"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Title
                                        </label>
                                        <input
                                            type="text"
                                            name="title"
                                            id="title"
                                            value={data.title}
                                            onChange={(e) =>
                                                setData("title", e.target.value)
                                            }
                                            className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 focus:outline-hidden sm:text-sm ${
                                                errors.title
                                                    ? "border-red-300"
                                                    : ""
                                            }`}
                                        />
                                        {errors.title && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.title}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-6 sm:col-span-3">
                                        <label
                                            htmlFor="tag"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Tag
                                        </label>
                                        <input
                                            type="text"
                                            name="tag"
                                            id="tag"
                                            value={data.tag}
                                            onChange={(e) =>
                                                setData("tag", e.target.value)
                                            }
                                            className={`mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
                                                errors.tag
                                                    ? "border-red-300"
                                                    : ""
                                            }`}
                                        />
                                        {errors.tag && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.tag}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-6 sm:col-span-3">
                                        <label
                                            htmlFor="read_time"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Read Time
                                        </label>
                                        <input
                                            type="text"
                                            name="read_time"
                                            id="read_time"
                                            placeholder="e.g., 5 min read"
                                            value={data.read_time}
                                            onChange={(e) =>
                                                setData(
                                                    "read_time",
                                                    e.target.value,
                                                )
                                            }
                                            className="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                        />
                                    </div>

                                    <div className="col-span-6 sm:col-span-3">
                                        <div className="flex items-center">
                                            <input
                                                id="is_featured"
                                                name="is_featured"
                                                type="checkbox"
                                                checked={data.is_featured}
                                                onChange={(e) =>
                                                    setData(
                                                        "is_featured",
                                                        e.target.checked,
                                                    )
                                                }
                                                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                            />
                                            <label
                                                htmlFor="is_featured"
                                                className="ml-2 block text-sm text-gray-900"
                                            >
                                                Feature in Discover Section
                                            </label>
                                        </div>
                                        <p className="mt-1 text-sm text-gray-500">
                                            Only 2 articles can be featured at a
                                            time
                                        </p>
                                    </div>

                                    {/* Author Information */}
                                    <div className="col-span-6 sm:col-span-3">
                                        <label
                                            htmlFor="author_name"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Author Name
                                        </label>
                                        <input
                                            type="text"
                                            name="author_name"
                                            id="author_name"
                                            value={data.writer.name}
                                            onChange={(e) =>
                                                setData("writer", {
                                                    ...data.writer,
                                                    name: e.target.value,
                                                })
                                            }
                                            className="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                        />
                                        {errors["writer.name"] && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors["writer.name"]}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-6 sm:col-span-3">
                                        <label
                                            htmlFor="author_bio"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Author Bio
                                        </label>
                                        <input
                                            type="text"
                                            name="author_bio"
                                            id="author_bio"
                                            value={data.writer.bio}
                                            onChange={(e) =>
                                                setData("writer", {
                                                    ...data.writer,
                                                    bio: e.target.value,
                                                })
                                            }
                                            className="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                        />
                                        {errors["writer.bio"] && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors["writer.bio"]}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-6">
                                        <label className="block text-sm font-medium text-gray-700">
                                            Featured Image
                                        </label>
                                        <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5 pb-6">
                                            <div className="space-y-1 text-center">
                                                {imagePreview ? (
                                                    <div className="relative">
                                                        <img
                                                            src={imagePreview}
                                                            alt="Preview"
                                                            className="mx-auto h-32 w-auto rounded-lg"
                                                        />
                                                        <button
                                                            type="button"
                                                            onClick={() => {
                                                                setImagePreview(
                                                                    null,
                                                                );
                                                                setData(
                                                                    "image",
                                                                    null,
                                                                );
                                                            }}
                                                            className="absolute top-0 right-0 -mt-2 -mr-2 rounded-full bg-red-500 p-1 text-xs text-white"
                                                        >
                                                            ×
                                                        </button>
                                                    </div>
                                                ) : (
                                                    <>
                                                        <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                                                        <div className="flex text-sm text-gray-600">
                                                            <label
                                                                htmlFor="image"
                                                                className="relative cursor-pointer rounded-md bg-white font-medium text-indigo-600 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:outline-none hover:text-indigo-500"
                                                            >
                                                                <span>
                                                                    Upload a
                                                                    file
                                                                </span>
                                                                <input
                                                                    id="image"
                                                                    name="image"
                                                                    type="file"
                                                                    accept="image/*"
                                                                    className="sr-only"
                                                                    onChange={
                                                                        handleImageChange
                                                                    }
                                                                />
                                                            </label>
                                                            <p className="pl-1">
                                                                or drag and drop
                                                            </p>
                                                        </div>
                                                        <p className="text-xs text-gray-500">
                                                            PNG, JPG, GIF up to
                                                            2MB
                                                        </p>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                        {errors.image && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.image}
                                            </p>
                                        )}
                                    </div>

                                    {/* Author Image */}
                                    <div className="col-span-6">
                                        <label className="block text-sm font-medium text-gray-700">
                                            Author Image
                                        </label>
                                        <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5 pb-6">
                                            <div className="space-y-1 text-center">
                                                {data.writer.avatar &&
                                                typeof data.writer.avatar ===
                                                    "string" ? (
                                                    <div className="relative">
                                                        <img
                                                            src={
                                                                data.writer
                                                                    .avatar
                                                            }
                                                            alt="Author avatar"
                                                            className="mx-auto h-32 w-32 rounded-full object-cover"
                                                        />
                                                        <button
                                                            type="button"
                                                            onClick={() => {
                                                                setData(
                                                                    "writer",
                                                                    {
                                                                        ...data.writer,
                                                                        avatar: null,
                                                                    },
                                                                );
                                                            }}
                                                            className="absolute top-0 right-0 -mt-2 -mr-2 rounded-full bg-red-500 p-1 text-xs text-white"
                                                        >
                                                            ×
                                                        </button>
                                                    </div>
                                                ) : (
                                                    <>
                                                        <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                                                        <div className="flex text-sm text-gray-600">
                                                            <label
                                                                htmlFor="author_image"
                                                                className="relative cursor-pointer rounded-md bg-white font-medium text-indigo-600 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:outline-none hover:text-indigo-500"
                                                            >
                                                                <span>
                                                                    Upload a
                                                                    file
                                                                </span>
                                                                <input
                                                                    id="author_image"
                                                                    name="author_image"
                                                                    type="file"
                                                                    accept="image/*"
                                                                    className="sr-only"
                                                                    onChange={(
                                                                        e,
                                                                    ) => {
                                                                        const file =
                                                                            e
                                                                                .target
                                                                                .files[0];
                                                                        if (
                                                                            file
                                                                        ) {
                                                                            setData(
                                                                                "writer",
                                                                                {
                                                                                    ...data.writer,
                                                                                    avatar: file,
                                                                                },
                                                                            );
                                                                        }
                                                                    }}
                                                                />
                                                            </label>
                                                            <p className="pl-1">
                                                                or drag and drop
                                                            </p>
                                                        </div>
                                                        <p className="text-xs text-gray-500">
                                                            PNG, JPG, GIF up to
                                                            2MB
                                                        </p>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                        {errors["writer.avatar"] && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors["writer.avatar"]}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Content Editor */}
                    <div className="bg-white px-4 py-5 shadow sm:rounded-lg sm:p-6">
                        <div className="md:grid md:grid-cols-3 md:gap-6">
                            <div className="md:col-span-1">
                                <h3 className="text-lg leading-6 font-medium text-gray-900">
                                    Article Content
                                </h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    Write the main content of your article
                                </p>
                            </div>
                            <div className="mt-5 md:col-span-2 md:mt-0">
                                <div className="grid grid-cols-6 gap-6">
                                    <div className="col-span-6">
                                        <label className="mb-2 block text-sm font-medium text-gray-700">
                                            Content
                                        </label>
                                        <Editor
                                            apiKey="no-api-key"
                                            value={data.content}
                                            onEditorChange={(content) =>
                                                setData("content", content)
                                            }
                                            init={{
                                                height: 400,
                                                menubar: false,
                                                plugins: [
                                                    "advlist",
                                                    "autolink",
                                                    "lists",
                                                    "link",
                                                    "image",
                                                    "charmap",
                                                    "preview",
                                                    "anchor",
                                                    "searchreplace",
                                                    "visualblocks",
                                                    "code",
                                                    "fullscreen",
                                                    "insertdatetime",
                                                    "media",
                                                    "table",
                                                    "code",
                                                    "help",
                                                    "wordcount",
                                                ],
                                                toolbar:
                                                    "undo redo | blocks | " +
                                                    "bold italic forecolor | alignleft aligncenter " +
                                                    "alignright alignjustify | bullist numlist outdent indent | " +
                                                    "removeformat | help",
                                                content_style:
                                                    "body { font-family:Helvetica,Arial,sans-serif; font-size:14px }",
                                            }}
                                        />
                                        {errors.content && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.content}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end">
                        <button
                            type="button"
                            onClick={() => router.visit("/admin/articles")}
                            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={processing}
                            className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50"
                        >
                            {processing ? "Updating..." : "Update Article"}
                        </button>
                    </div>
                </form>
            </div>
        </>
    );
};

export default Edit;
