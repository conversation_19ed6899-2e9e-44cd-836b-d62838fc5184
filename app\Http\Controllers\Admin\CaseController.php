<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PortfolioCase;
use App\Models\PortfolioTag;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class CaseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $cases = PortfolioCase::with('portfolioTags')->latest()->paginate(10);
        return Inertia::render('Admin/Cases/Index', compact('cases'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $portfolioTags = PortfolioTag::active()->orderBy('name')->get();

        return Inertia::render('Admin/Cases/Create', [
            'portfolioTags' => $portfolioTags,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'company_name' => 'required|string|max:255',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:255',
            'portfolio_tags' => 'nullable|array',
            'portfolio_tags.*' => 'exists:portfolio_tags,id',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images' => 'required|array|min:1',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_published' => 'nullable|boolean',
        ]);

        // Set default value for is_published if not provided
        $validated['is_published'] = $validated['is_published'] ?? false;

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('cases/logos', 'public');
            $validated['logo'] = '/storage/' . $logoPath;
        }

        // Handle multiple image uploads
        $imagePaths = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $imagePath = $image->store('cases/images', 'public');
                $imagePaths[] = '/storage/' . $imagePath;
            }
        }
        $validated['images'] = $imagePaths;
        $validated['thumbnail'] = $imagePaths[0] ?? null; // First image as thumbnail

        // Generate slug
        $validated['slug'] = Str::slug($validated['title']);

        // Extract portfolio tags before creating
        $portfolioTags = $validated['portfolio_tags'] ?? [];
        unset($validated['portfolio_tags']);

        $case = PortfolioCase::create($validated);

        // Sync portfolio tags
        if (!empty($portfolioTags)) {
            $case->portfolioTags()->sync($portfolioTags);
        }

        return redirect()->route('admin.cases.index')->with('success', 'Case created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(PortfolioCase $case)
    {
        return inertia('Admin/Cases/Show', compact('case'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PortfolioCase $case)
    {
        $portfolioTags = PortfolioTag::active()->orderBy('name')->get();
        $case->load('portfolioTags');

        return Inertia::render('Admin/Cases/Edit', [
            'case' => $case,
            'portfolioTags' => $portfolioTags,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PortfolioCase $case)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'company_name' => 'required|string|max:255',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:255',
            'portfolio_tags' => 'nullable|array',
            'portfolio_tags.*' => 'exists:portfolio_tags,id',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'existing_images' => 'nullable|array',
            'existing_images.*' => 'string',
            'is_published' => 'boolean',
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($case->logo && Storage::disk('public')->exists(str_replace('/storage/', '', $case->logo))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $case->logo));
            }

            $logoPath = $request->file('logo')->store('cases/logos', 'public');
            $validated['logo'] = '/storage/' . $logoPath;
        }

        // Handle image updates
        $finalImages = $validated['existing_images'] ?? [];

        // Add new images if uploaded
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $imagePath = $image->store('cases/images', 'public');
                $finalImages[] = '/storage/' . $imagePath;
            }
        }

        // Delete removed images
        if ($case->images) {
            foreach ($case->images as $oldImage) {
                if (!in_array($oldImage, $finalImages)) {
                    if (Storage::disk('public')->exists(str_replace('/storage/', '', $oldImage))) {
                        Storage::disk('public')->delete(str_replace('/storage/', '', $oldImage));
                    }
                }
            }
        }

        $validated['images'] = $finalImages;
        $validated['thumbnail'] = $finalImages[0] ?? null;

        // Update slug if title changed
        if ($validated['title'] !== $case->title) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Extract portfolio tags before updating
        $portfolioTags = $validated['portfolio_tags'] ?? [];
        unset($validated['portfolio_tags']);

        $case->update($validated);

        // Sync portfolio tags
        $case->portfolioTags()->sync($portfolioTags);

        return redirect()->route('admin.cases.index')->with('success', 'Case updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PortfolioCase $case)
    {
        // Delete associated images
        if ($case->images) {
            foreach ($case->images as $image) {
                if (Storage::disk('public')->exists(str_replace('/storage/', '', $image))) {
                    Storage::disk('public')->delete(str_replace('/storage/', '', $image));
                }
            }
        }

        // Delete logo
        if ($case->logo && Storage::disk('public')->exists(str_replace('/storage/', '', $case->logo))) {
            Storage::disk('public')->delete(str_replace('/storage/', '', $case->logo));
        }

        $case->delete();

        return redirect()->route('admin.cases.index')->with('success', 'Case deleted successfully.');
    }
}
