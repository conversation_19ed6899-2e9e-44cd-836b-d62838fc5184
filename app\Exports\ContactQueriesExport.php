<?php

namespace App\Exports;

use App\Models\ContactQuery;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ContactQueriesExport implements FromCollection, WithHeadings, WithMapping
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return ContactQuery::all();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'First Name',
            'Last Name',
            'Company Name',
            'Phone Number',
            'Email',
            'Annual Revenue',
            'Message',
            'Submitted At',
        ];
    }

    /**
     * @param ContactQuery $contactQuery
     * @return array
     */
    public function map($contactQuery): array
    {
        return [
            $contactQuery->id,
            $contactQuery->first_name,
            $contactQuery->last_name,
            $contactQuery->company_name,
            $contactQuery->phone_number,
            $contactQuery->email,
            $contactQuery->annual_revenue,
            $contactQuery->message,
            $contactQuery->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
