<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Article;
use App\Models\PortfolioCase;
use App\Models\ContactQuery;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test article
        Article::create([
            'slug' => 'test-article',
            'title' => 'Test Article for Admin Panel',
            'description' => 'This is a test article created to demonstrate the admin panel functionality.',
            'tag' => 'Test',
            'content' => [
                ['type' => 'paragraph', 'content' => 'This is the first paragraph of our test article.'],
                ['type' => 'heading', 'content' => 'Test Heading'],
                ['type' => 'paragraph', 'content' => 'This is another paragraph with more content to test the admin panel.'],
            ],
            'image' => '/images/placeholder.jpg',
            'date' => now()->format('M d, Y'),
            'read_time' => '3 min read',
        ]);

        // Create test portfolio case
        PortfolioCase::create([
            'slug' => 'test-portfolio-case',
            'title' => 'Test Portfolio Case',
            'description' => 'This is a test portfolio case to demonstrate the cases management functionality.',
            'company_name' => 'Test Company Ltd.',
            'images' => ['/images/placeholder.jpg', '/images/placeholder.jpg'],
            'thumbnail' => '/images/placeholder.jpg',
            'tags' => ['Web Development', 'Design', 'Laravel'],
            'is_published' => true,
        ]);

        // Create test contact query
        ContactQuery::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'company_name' => 'Example Corp',
            'phone_number' => '+****************',
            'annual_revenue' => '$100K - $500K',
            'message' => 'Hello, I am interested in your web development services. Could you please provide more information about your packages and pricing?',
        ]);

        // Create another contact query
        ContactQuery::create([
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'company_name' => 'Test Company Inc.',
            'phone_number' => '+****************',
            'annual_revenue' => '$1M+',
            'message' => 'We are looking for a digital marketing partner to help us grow our online presence. Please contact us to discuss our requirements.',
        ]);
    }
}
